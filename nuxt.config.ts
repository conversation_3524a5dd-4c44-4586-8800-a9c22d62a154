export default defineNuxtConfig({
  ssr: false,
  modules: ['@nuxtjs/tailwindcss','@pinia/nuxt', 'shadcn-nuxt',],
  spaLoadingTemplate: 'spa-loading-template.html',

  shadcn: {
    /**
     * Prefix for all the imported component
     */
    prefix: '',
    /**
     * Directory that the component lives in.
     * @default "./components/ui"
     */
    componentDir: './components/ui'
  },

  css: ['~/assets/css/tailwind.css'],

  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},

    },

  },

  runtimeConfig: {
    B4A_APP_ID: process.env.NUXT_B4A_APP_ID,
    B4A_API_KEY: process.env.NUXT_B4A_API_KEY,
    B4A_MASTER_KEY: process.env.NUXT_B4A_MASTER_KEY,
    MG_API_KEY: process.env.NUXT_MG_API_KEY,
    INFLUX_SERVER_BASE_URL: process.env.NUXT_INFLUX_SERVER_BASE_URL,
    PAYSTACK_BEARER_TOKEN: process.env.NUXT_PAYSTACK_BEARER_TOKEN,
    public: {
      API_BASE_URL: process.env.NUXT_PUBLIC_API_BASE_URL,
      APP_BASE_URL: process.env.NUXT_PUBLIC_APP_BASE_URL,

    }

  },

  routeRules: {
    '/api/**': { cors: true },
    '/': { redirect: '/auth/login' },
  },

  imports: {
    dirs: [
      '@/stores/*',
      '@/components/ui'
    ]
  },

  compatibilityDate: '2025-07-23',
})