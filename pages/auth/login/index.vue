<template>
    <section class="py-12 bg-white sm:py-16 lg:py-20">
        <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
            <div class="max-w-sm mx-auto">
                <div class="text-center">
                    <img class="w-36 mx-auto" src="/img/logo_b.png" alt="Shuttle Admin Logo" />
                    <h1 class="mt-12 text-3xl font-bold text-gray-900">Welcome Back</h1>
                    <p class="mt-4 text-sm font-medium text-gray-500">Manage shuttle bookings efficiently.</p>

                    <!-- Session expiry alert -->
                    <Alert v-if="sessionExpiredReason" class="mt-4" variant="destructive">
                        <AlertCircle class="h-4 w-4" />
                        <AlertTitle>Session Expired</AlertTitle>
                        <AlertDescription>
                            {{ sessionExpiredReason === 'inactivity' ?
                                'Your session has expired due to inactivity.' :
                                'Your authentication session has expired.' }}
                        </AlertDescription>
                    </Alert>
                </div>

                <!-- <div class="mt-12">
                <button
                    type="button"
                    class="inline-flex items-center justify-center w-full px-6 py-3 text-sm font-semibold leading-5 text-gray-600 transition-all duration-200 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 hover:bg-gray-50 hover:text-gray-900"
                >
                    <img class="w-5 h-5 mr-2" src="https://landingfoliocom.imgix.net/store/collection/clarity-dashboard/images/previews/sign-in/1/google-logo.svg" alt="" />
                    Sign in with Google
                </button>
            </div>

            <div class="relative mt-6">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-200"></div>
                </div>

                <div class="relative flex justify-center">
                    <span class="px-2 text-sm text-gray-400 bg-white"> or </span>
                </div>
            </div> -->


                    <form @submit.prevent="onSubmit" class="space-y-4">
                        <FormField v-slot="{ field, errorMessage }" name="email">
                            <FormItem>
                                <FormLabel class="text-sm font-bold text-gray-900">Email</FormLabel>
                                <FormControl>
                                    <Input
                                        type="email"
                                        placeholder="Email address"
                                        v-bind="field"
                                        class="border block w-full px-4 py-3 placeholder-gray-500 border-gray-300 rounded-lg focus:ring-indigo-600 focus:border-indigo-600 sm:text-sm caret-indigo-600"
                                        :class="{ 'border-red-500': errorMessage }"
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        </FormField>

                        <FormField v-slot="{ field, errorMessage }" name="password">
                            <FormItem>
                                <div class="flex items-center justify-between">
                                    <FormLabel class="text-sm font-bold text-gray-900">Password</FormLabel>
                                    <NuxtLink to="/auth/forgot-password" title="" class="text-sm font-medium text-indigo-600 hover:text-indigo-700">
                                        Forgot Password?
                                    </NuxtLink>
                                </div>
                                <FormControl>
                                    <div class="relative">
                                        <Input
                                            :type="showPassword ? 'text' : 'password'"
                                            placeholder="Password (min. 8 character)"
                                            v-bind="field"
                                            class="border block w-full px-4 py-3 pr-12 placeholder-gray-500 border-gray-300 rounded-lg focus:ring-indigo-600 focus:border-indigo-600 sm:text-sm caret-indigo-600"
                                            :class="{ 'border-red-500': errorMessage }"
                                        />
                                        <button
                                            type="button"
                                            @click="togglePasswordVisibility"
                                            class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 focus:outline-none"
                                        >
                                            <Eye v-if="!showPassword" class="h-5 w-5" />
                                            <EyeOff v-else class="h-5 w-5" />
                                        </button>
                                    </div>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        </FormField>

                        <FormField v-slot="{ field }" name="rememberMe">
                            <div class="relative flex items-center">
                                <FormControl>
                                    <div class="flex items-center h-5">
                                        <Input
                                            type="checkbox"
                                            id="remember-password"
                                            v-bind="field"
                                            class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-600"
                                        />
                                    </div>
                                </FormControl>
                                <div class="ml-3">
                                    <FormLabel for="remember-password" class="text-sm font-medium text-gray-900">
                                        Remember Me
                                    </FormLabel>
                                </div>
                            </div>
                        </FormField>

                        <div>
                            <Button
                                type="submit"
                                :disabled="authStore.isLoggingUserIn || authStore.success_LoginUser"
                                class="inline-flex items-center justify-center w-full px-6 py-3 text-sm font-semibold leading-5 text-white transition-all duration-200 bg-indigo-600 border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-600 hover:bg-indigo-500"
                            >
                                <Loader2 v-if="authStore.isLoggingUserIn" class="ml-2 animate-spin" :size="16"></Loader2>
                                <span v-else>Sign In</span>
                            </Button>
                        </div>
                    </form>


                <!-- <div class="mt-6 text-center">
                <p class="text-sm font-medium text-gray-900">Don't have an account? <a href="#" title="" class="font-bold hover:underline"> Sign up now </a></p>
            </div> -->
            </div>
        </div>
    </section>
</template>

<script setup lang="ts">
import { useAuthStore } from '~/stores/auth/auth.store';
import { Loader2, AlertCircle, Eye, EyeOff } from 'lucide-vue-next'
import { toast } from '@/components/ui/toast';
import { onMounted, ref } from 'vue';
import * as z from 'zod';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
} from '@/components/ui/form';

definePageMeta({
    middleware: "already-auth"
})

useHead(
    {
        title: 'Sign In - Shuttle Booking Admin'
    }
)

const authStore = useAuthStore()
const sessionExpiredReason = ref<'expired' | 'inactivity' | null>(null)
const showPassword = ref(false)

// Toggle password visibility
const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value
}

// Define the form validation schema using Zod
const formSchema = toTypedSchema(z.object({
    email: z.string()
        .email('Please enter a valid email address')
        .min(1, 'Email is required'),
    password: z.string()
        .min(8, 'Password must be at least 8 characters')
        .min(1, 'Password is required'),
    rememberMe: z.boolean().optional()
}))

// Initialize the form with vee-validate
const { handleSubmit } = useForm({
    validationSchema: formSchema,
    initialValues: {
        email: '',
        password: '',
        rememberMe: false
    }
})

// Check if session expired on component mount
onMounted(() => {
    // In Nuxt 3, we should use the useNuxtApp composable to check if we're on client-side
    if (typeof window !== 'undefined') {
        const sessionExpired = localStorage.getItem('session_expired');
        if (sessionExpired === 'expired' || sessionExpired === 'inactivity') {
            sessionExpiredReason.value = sessionExpired as 'expired' | 'inactivity';
            // Clear the flag
            localStorage.removeItem('session_expired');
        }
    }
})

// Define the login submission handler
const onSubmit = handleSubmit(async (formValues) => {
    try {
        await authStore.loginWithEmail({
            email: formValues.email,
            password: formValues.password
        });

        if (authStore.failed_LoginUser) {
            toast({
                title: 'Shuttle Admin Login Failed',
                description: authStore.loginFailure.message,
            });
            return;
        }

        // Store remember me preference if selected
        if (formValues.rememberMe && typeof window !== 'undefined') {
            localStorage.setItem('remember_user', 'true');
        }

        return useRouter().go(0); // Middleware will handle it from here
    } catch (error) {
        toast({
            title: 'Login Error',
            description: 'An unexpected error occurred. Please try again.',
        });
    }
});

</script>