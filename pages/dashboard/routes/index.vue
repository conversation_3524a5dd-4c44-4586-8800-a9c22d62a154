<template>
    <RoutesTheme>
    <AnalyticsBasePageLayout
        title="Route Management & Analytics"
        subtitle="Comprehensive route performance analytics and optimization insights"
        new-button-text="Create Route"
        table-title="Route Overview"
        :table-subtitle="`${filteredRoutes.length} of ${routesData?.length || 0} routes`"
        loading-text="Loading route analytics..."
        details-sheet-title="Route Details"
        details-sheet-description="View detailed information about this route"
        insights-sheet-title="Route Insights & Analytics"
        insights-sheet-description="Route performance analytics, demand insights, and optimization recommendations"
        @export="handleExport"
        ref="pageLayoutRef"
    >
        <!-- Header Actions -->
        <template #header-actions>
            <NewRouteButton />
        </template>

        <!-- Metrics Cards -->
        <template #metrics-cards>
            <RouteAnalyticsMetricsCards :routes="routesData ?? []" />
        </template>

        <!-- Trends Chart -->
        <template #trends-chart>
            <RouteAnalyticsTrendsChart :routes="routesData ?? []" />
        </template>

        <!-- Filters -->
        <template #filters>
            <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Route Filters</h3>
                    <Button variant="outline" size="sm" @click="clearFilters">
                        Clear All
                    </Button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <Input
                            v-model="searchQuery"
                            placeholder="Route name, origin, destination..."
                            class="w-full"
                        />
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <Select v-model="statusFilter">
                            <SelectTrigger>
                                <SelectValue placeholder="All statuses" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All statuses</SelectItem>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                                <SelectItem value="suspended">Suspended</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <!-- Price Range Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Price Range</label>
                        <Select v-model="priceFilter">
                            <SelectTrigger>
                                <SelectValue placeholder="All prices" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All prices</SelectItem>
                                <SelectItem value="low">Low (< $50)</SelectItem>
                                <SelectItem value="medium">Medium ($50-$150)</SelectItem>
                                <SelectItem value="high">High (> $150)</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <!-- Demand Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Demand Level</label>
                        <Select v-model="demandFilter">
                            <SelectTrigger>
                                <SelectValue placeholder="All demand levels" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All demand levels</SelectItem>
                                <SelectItem value="high">High Demand</SelectItem>
                                <SelectItem value="medium">Medium Demand</SelectItem>
                                <SelectItem value="low">Low Demand</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>
        </template>

        <!-- Data Table -->
        <template #data-table>
            <div v-if="routesPending" class="w-full h-60 flex justify-center items-center">
                <Loader2 class="animate-spin h-8 w-8" />
            </div>
            <template v-else-if="routesData && routesData.length > 0">
                <TripDataRender
                    :columns="columns"
                    :data="filteredRoutes"
                    :is-loading="routesPending"
                    @get-table-data="handleDataTableData"
                    @get-row-data="handleRowClicked"
                />
            </template>
            <div v-else class="w-full h-60 flex justify-center items-center">
                <div class="text-center">
                    <MapPin class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-500">No routes found</p>
                </div>
            </div>
        </template>

        <!-- Details Content -->
        <template #details-content>
            <RouteInfo
                v-if="selectedRoute"
                :route="selectedRoute"
                @close="pageLayoutRef?.closeDetailsSheet()"
            />
        </template>

        <!-- Insights Content -->
        <template #insights-content>
            <RouteAnalyticsInsightsSidebar
                :routes="routesData ?? []"
                :is-sheet="true"
                @close="pageLayoutRef?.closeInsightsSheet()"
            />
        </template>
    </AnalyticsBasePageLayout>
    </RoutesTheme>
</template>

<script setup lang="ts">
import { useRouteStore } from '~/stores/route/route.store'
import { Loader2, MapPin } from 'lucide-vue-next';
import type { IRoute } from '~/stores/route/model/route.model';
import { useShuttleStore } from '~/stores/shuttle/shuttle.store';
import type { ColumnDef, Row, Table } from '@tanstack/vue-table'
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';

useHead({
    title: 'Route Management & Analytics'
})

definePageMeta({
    middleware: "auth"
})

const routeStore = useRouteStore()
const shuttleStore = useShuttleStore();

const { pending: routesPending, status: routesStatus, data: routesData, error, refresh } = useAsyncData<IRoute[]>('routes', () => routeStore.getRoutes(), { lazy: true })

// Watch whenever there is a successful route addition and refresh the data
watch(() => routeStore.successNewRoute, (newValue) => {
    if (newValue) {
        refresh();
    }
});

// Page layout reference
const pageLayoutRef = ref()

// Filter states
const searchQuery = ref('')
const statusFilter = ref('')
const priceFilter = ref('')
const demandFilter = ref('')

// Filtered routes
const filteredRoutes = computed(() => {
    if (!routesData.value) return [];

    let filtered = [...routesData.value];

    // Apply search filter
    if (searchQuery.value) {
        const search = searchQuery.value.toLowerCase();
        filtered = filtered.filter(route =>
            route.name?.toLowerCase().includes(search) ||
            route.origin?.name?.toLowerCase().includes(search) ||
            route.destination?.name?.toLowerCase().includes(search) ||
            route.description?.toLowerCase().includes(search)
        );
    }

    // Apply status filter
    if (statusFilter.value) {
        filtered = filtered.filter(route =>
            route.status?.toLowerCase() === statusFilter.value.toLowerCase()
        );
    }

    // Apply price filter
    if (priceFilter.value) {
        filtered = filtered.filter(route => {
            const price = route.base_price || 0;
            switch (priceFilter.value) {
                case 'low': return price < 50;
                case 'medium': return price >= 50 && price <= 150;
                case 'high': return price > 150;
                default: return true;
            }
        });
    }

    // Apply demand filter (mock logic based on price - in real app, use actual demand data)
    if (demandFilter.value) {
        filtered = filtered.filter(route => {
            const price = route.base_price || 0;
            switch (demandFilter.value) {
                case 'high': return price > 100; // Higher price routes assumed high demand
                case 'medium': return price >= 50 && price <= 100;
                case 'low': return price < 50;
                default: return true;
            }
        });
    }

    return filtered;
});

// Clear all filters
const clearFilters = () => {
    searchQuery.value = '';
    statusFilter.value = '';
    priceFilter.value = '';
    demandFilter.value = '';
};

// Handle export
const handleExport = () => {
    // Implement route export functionality
    console.log('Exporting route data...');
};

// Selected route for details
const selectedRoute = ref<IRoute | null>(null);

// SHEET CONTROL
const isSheetDialogueOpen = ref(false)
const handleOnSheetDialogOpen = (isOpen: boolean) => {
    isSheetDialogueOpen.value = isOpen
}
// end of SHEET CONTROL

// DIALOG CONTROL
const isDialogueOpen = ref(false)
const handleOnDialogOpen = (isOpen: boolean) => {
    isDialogueOpen.value = isOpen
}
// end of DIALOG CONTROL


// ROUTE RENDER TABLE DATA
const selectedRow = ref<Row<IRoute>>()
const dataTableRef = ref<Table<IRoute>>()

const handleDataTableData = (data: Table<IRoute>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<IRoute>) => {
    // Set selected route
    selectedRoute.value = row.original;

    // Open details sheet
    pageLayoutRef.value?.openDetailsSheet();


}

const columns: ColumnDef<IRoute>[] = [
    {
        accessorKey: 'objectId',
        header: 'Id',

    },
    {
        accessorKey: 'createdAt',
    },
    {

        accessorKey: 'baseFare',
    },
    {

        accessorKey: 'origin.name',
    },
    {
        accessorKey: 'destination.name',
    },
    {
        accessorKey: 'status',
    },
   


]
// end of DATA TABLE STUFF

const { isMobile } = useMobileDetect()

</script>