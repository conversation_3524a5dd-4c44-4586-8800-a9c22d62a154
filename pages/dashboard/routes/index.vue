<template>
  <NuxtLayout name="dashboard">
    <div class="min-h-screen bg-transparent">
      <!-- Header -->
      <div class="bg-white border-b border-gray-200">
        <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
          <div class="py-6 sm:flex sm:items-center sm:justify-between">
            <div class="flex items-center space-x-4">
              <div class="p-3 bg-gray-100 rounded-xl">
                <MapPin class="h-8 w-8 text-gray-600" />
              </div>
              <div>
                <h1 class="text-2xl font-bold text-gray-900">Routes Management</h1>
                <p class="mt-1 text-sm text-gray-500">
                  Comprehensive route performance analytics and optimization insights
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-3 mt-4 sm:mt-0">
              <NewRouteButton />
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8 py-6">
        <!-- Analytics Dashboard -->
        <div class="space-y-6">
          <!-- Key Metrics Cards -->
          <RouteAnalyticsMetricsCards :routes="routesData ?? []" v-if="status === 'success' && !error" />

          <!-- Routes Table -->
          <div v-if="status === 'pending'" class="w-full h-60 flex justify-center items-center">
            <Loader2 class="animate-spin h-8 w-8" />
          </div>
          <div v-else-if="error" class="w-full h-60 flex justify-center items-center">
            <div class="text-center">
              <AlertTriangle class="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p class="text-gray-500">Error loading routes: {{ error.message }}</p>
            </div>
          </div>
          <div v-else-if="routesData && routesData.length > 0" class="bg-white rounded-lg">
            <div class="p-6 rounded-lg border border-gray-200 mb-5">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">All Routes</h3>
                  <p class="text-sm text-gray-500">
                    {{ routesData.length }} of {{ routesData.length }} routes
                  </p>
                </div>
                <div class="flex items-center space-x-2">
                  <Button variant="outline" size="sm" @click="handleExport">
                    <Download class="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </div>
            <TripDataRender :columns="columns" :data="filteredRoutes" :is-loading="isLoading"
              @get-table-data="handleDataTableData" @get-row-data="handleRowClicked" />
          </div>
          <div v-else class="w-full h-60 flex justify-center items-center">
            <div class="text-center">
              <MapPin class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">No routes found</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { useRouteStore } from '~/stores/route/route.store';
import { Loader2, MapPin, Download, AlertTriangle } from 'lucide-vue-next';
import type { IRoute } from '~/stores/route/model/route.model';
import { useShuttleStore } from '~/stores/shuttle/shuttle.store';
import type { ColumnDef, Row, Table } from '@tanstack/vue-table';
import { Button } from '@/components/ui/button';

useHead({
  title: 'Route Management & Analytics',
});

definePageMeta({
  middleware: 'auth',
});

const routeStore = useRouteStore();
const shuttleStore = useShuttleStore();

// Fetch routes using useAsyncData
const { data: routesData, status, error, refresh } = await useAsyncData('routes', () => {
  return routeStore.getRoutes();
}, {
  lazy: true,
});

const isLoading = computed(() => status.value === 'pending');


// Watch for successful route addition and refresh data
watch(() => routeStore.successNewRoute, (newValue) => {
  if (newValue) {
    refresh();
  }
});

// Page layout reference
const pageLayoutRef = ref();

// Filter states
const searchQuery = ref('');
const statusFilter = ref('');
const priceFilter = ref('');
const demandFilter = ref('');

// Filtered routes
const filteredRoutes = computed(() => {
  if (!routesData.value) return [];

  let filtered = [...routesData.value];

  // Apply search filter
  if (searchQuery.value) {
    const search = searchQuery.value.toLowerCase();
    filtered = filtered.filter(route =>
      route.name?.toLowerCase().includes(search) ||
      route.origin?.name?.toLowerCase().includes(search) ||
      route.destination?.name?.toLowerCase().includes(search) ||
      route.description?.toLowerCase().includes(search)
    );
  }

  // Apply status filter
  if (statusFilter.value) {
    filtered = filtered.filter(route =>
      route.status?.toLowerCase() === statusFilter.value.toLowerCase()
    );
  }

  // Apply price filter
  if (priceFilter.value) {
    filtered = filtered.filter(route => {
      const price = route.base_price || 0;
      switch (priceFilter.value) {
        case 'low': return price < 50;
        case 'medium': return price >= 50 && price <= 150;
        case 'high': return price > 150;
        default: return true;
      }
    });
  }

  // Apply demand filter (mock logic based on price)
  if (demandFilter.value) {
    filtered = filtered.filter(route => {
      const price = route.base_price || 0;
      switch (demandFilter.value) {
        case 'high': return price > 100;
        case 'medium': return price >= 50 && price <= 100;
        case 'low': return price < 50;
        default: return true;
      }
    });
  }

  return filtered;
});

// Clear all filters
const clearFilters = () => {
  searchQuery.value = '';
  statusFilter.value = '';
  priceFilter.value = '';
  demandFilter.value = '';
};

// Handle export
const handleExport = () => {
  console.log('Exporting route data...');
};

// Selected route for details
const selectedRoute = ref<IRoute | null>(null);

// SHEET CONTROL
const isSheetDialogueOpen = ref(false);
const handleOnSheetDialogOpen = (isOpen: boolean) => {
  isSheetDialogueOpen.value = isOpen;
};

// DIALOG CONTROL
const isDialogueOpen = ref(false);
const handleOnDialogOpen = (isOpen: boolean) => {
  isDialogueOpen.value = isOpen;
};

// ROUTE RENDER TABLE DATA
const selectedRow = ref<Row<IRoute>>();
const dataTableRef = ref<Table<IRoute>>();

const handleDataTableData = (data: Table<IRoute>) => (dataTableRef.value = data);
const handleRowClicked = async (row: Row<IRoute>) => {
  selectedRoute.value = row.original;
  pageLayoutRef.value?.openDetailsSheet();
};

const columns: ColumnDef<IRoute>[] = [
  { accessorKey: 'objectId', header: 'Id' },
  { accessorKey: 'createdAt' },
  { accessorKey: 'baseFare' },
  { accessorKey: 'origin.name' },
  { accessorKey: 'destination.name' },
  { accessorKey: 'status' },
];

const { isMobile } = useMobileDetect();
</script>