<template>
    <NuxtLayout name="dashboard">
        <DashboardTheme>
            <div class="min-h-screen bg-transparent">
                <!-- Header -->
                <div class="bg-white border-b border-gray-200">
                    <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                        <div class="py-6 sm:flex sm:items-center sm:justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="p-3 bg-gray-100 rounded-xl">
                                    <Home class="h-8 w-8 text-gray-600" />
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-gray-900">Business Dashboard</h1>
                                    <p class="mt-1 text-sm text-gray-500">
                                        Comprehensive overview of your shuttle business performance
                                    </p>
                                </div>
                            </div>
                            <!-- <div class="flex items-center space-x-3 mt-4 sm:mt-0">
                                <Button variant="outline" @click="toggleInsights">
                                    <BarChart3 class="h-4 w-4 mr-2" />
                                    {{ showInsights ? 'Hide' : 'Show' }} Insights
                                </Button>
                                <Button>
                                    <Plus class="h-4 w-4 mr-2" />
                                    Quick Actions
                                </Button>
                            </div> -->
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8 py-6">
                    <!-- Analytics Dashboard -->
                    <Suspense>
                        <template #default>
                            <div class="space-y-6">
                                <!-- Key Metrics Cards -->
                                <DashboardAnalyticsMetricsCards :stats="organizationStats" :loading="statsLoading === 'pending'" />

                                <!-- Trends Chart -->
                                <DashboardAnalyticsTrendsChart />

                                <!-- Business Overview Sections -->
                                <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                                    <!-- Recent Bookings -->
                                    <div class="bg-white rounded-lg border border-gray-200">
                                        <div class="p-6">
                                            <div class="flex items-center justify-between mb-4">
                                                <h3 class="text-lg font-semibold text-gray-900">Recent Bookings</h3>
                                                <NuxtLink to="/dashboard/bookings"
                                                    class="text-sm text-blue-600 hover:text-blue-800">
                                                    View All →
                                                </NuxtLink>
                                            </div>
                                            <DashboardRecentBookings />
                                        </div>
                                    </div>

                                    <!-- Today's Schedules -->
                                    <div class="bg-white rounded-lg border border-gray-200">
                                        <DashboardShuttleSchedules  />
                                    </div>
                                </div>

                                <!-- Additional Dashboard Sections -->
                                <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                                    <!-- Customer Overview -->
                                    <div class="bg-white rounded-lg border border-gray-200">
                                        <DashboardRecentCustomers />
                                    </div>

                                    <!-- Payouts Summary -->
                                    <div class="bg-white rounded-lg border border-gray-200">
                                        <DashboardPayoutsSummary />
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template #fallback>
                            <div class="w-full h-96 flex justify-center items-center">
                                <div class="flex flex-col items-center">
                                    <Loader2 class="animate-spin h-8 w-8 mb-2" />
                                    <p class="text-gray-500">Loading dashboard analytics...</p>
                                </div>
                            </div>
                        </template>
                    </Suspense>
                </div>

                <!-- Insights Sheet -->
                <Sheet :open="showInsights" @update:open="handleInsightsSheetChange">
                    <SheetContent :class="[
                        'h-full bg-white overflow-y-auto',
                        isMobile ? 'fixed inset-x-0 bottom-0 mt-24 rounded-t-[10px]' : 'sm:max-w-[900px] rounded-lg'
                    ]" :side="isMobile ? 'bottom' : 'right'">
                        <SheetHeader>
                            <SheetTitle>Business Insights & Analytics</SheetTitle>
                            <SheetDescription>
                                Real-time business analytics, performance metrics, and actionable insights
                            </SheetDescription>
                        </SheetHeader>
                        <div class="p-0">
                            <!-- <DashboardAnalyticsInsightsSidebar :booking-count="bookingCount"
                                :transactions-sum="transactionsSum" :route-count="routeCount"
                                :shuttle-count="shuttleCount" :today-bookings="todayBookings"
                                :available-drivers="availableDrivers" :is-sheet="true" @close="showInsights = false" /> -->
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        </DashboardTheme>
    </NuxtLayout>
</template>
<script setup lang="ts">
import { Loader2, Home } from 'lucide-vue-next';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetTitle
} from '@/components/ui/sheet';
import { useOrganizationStore } from '~/stores/organization/organization.store';

useHead({
    title: "Business Dashboard"
})

definePageMeta({
    middleware: "auth"
})

// Check if the device is mobile
const isMobile = ref(false)
onMounted(() => {
    isMobile.value = window.innerWidth < 768
    window.addEventListener('resize', () => {
        isMobile.value = window.innerWidth < 768
    })
})

// Insights functionality
const showInsights = ref(false);
const toggleInsights = () => {
    showInsights.value = !showInsights.value;
};

// Handle insights sheet open/close
const handleInsightsSheetChange = (isOpen: boolean) => {
    showInsights.value = isOpen;
};

// Get organization stats
const organizationStore = useOrganizationStore();
const { data: organizationStats, status: statsLoading } = useAsyncData(
    'dashboard-organizationStats',
    () => organizationStore.getOrgStats(),
    { lazy: true, server: false }
);


</script>