<template>
    <NuxtLayout name="dashboard">
        <DashboardTheme>
            <div class="min-h-screen bg-transparent">
                <!-- Header -->
                <div class="bg-white border-b border-gray-200">
                    <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                        <div class="py-6 sm:flex sm:items-center sm:justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="p-3 bg-gray-100 rounded-xl">
                                    <Home class="h-8 w-8 text-gray-600" />
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-gray-900">Business Dashboard</h1>
                                    <p class="mt-1 text-sm text-gray-500">
                                        Comprehensive overview of your shuttle business performance
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 mt-4 sm:mt-0">
                                <Button variant="outline" @click="toggleInsights">
                                    <BarChart3 class="h-4 w-4 mr-2" />
                                    {{ showInsights ? 'Hide' : 'Show' }} Insights
                                </Button>
                                <Button>
                                    <Plus class="h-4 w-4 mr-2" />
                                    Quick Actions
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8 py-6">
                    <!-- Analytics Dashboard -->
                    <Suspense>
                        <template #default>
                            <div class="space-y-6">
                                <!-- Key Metrics Cards -->
                                <DashboardAnalyticsMetricsCards />

                                <!-- Trends Chart -->
                                <DashboardAnalyticsTrendsChart :booking-count="bookingCount"
                                    :transactions-sum="transactionsSum" :route-count="routeCount"
                                    :shuttle-count="shuttleCount" />

                                <!-- Business Overview Sections -->
                                <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                                    <!-- Recent Bookings -->
                                    <div class="bg-white rounded-lg border border-gray-200">
                                        <div class="p-6">
                                            <div class="flex items-center justify-between mb-4">
                                                <h3 class="text-lg font-semibold text-gray-900">Recent Bookings</h3>
                                                <NuxtLink to="/dashboard/bookings"
                                                    class="text-sm text-blue-600 hover:text-blue-800">
                                                    View All →
                                                </NuxtLink>
                                            </div>
                                            <DashboardRecentBookings :bookings="recentBookings"
                                                :is-loading="bookingStore.isLoading" />
                                        </div>
                                    </div>

                                    <!-- Today's Schedules -->
                                    <div class="bg-white rounded-lg border border-gray-200">
                                        <DashboardShuttleSchedules :schedules="todaySchedules"
                                            :is-loading="scheduleStore.isLoading" />
                                    </div>
                                </div>

                                <!-- Additional Dashboard Sections -->
                                <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                                    <!-- Customer Overview -->
                                    <div class="bg-white rounded-lg border border-gray-200">
                                        <DashboardRecentCustomers :customers="recentCustomers"
                                            :is-loading="customerStore.isLoading"
                                            :total-customers="customerCount?.total || 0"
                                            :new-this-month="customerCount?.newThisMonth || 0" />
                                    </div>

                                    <!-- Payouts Summary -->
                                    <div class="bg-white rounded-lg border border-gray-200">
                                        <DashboardPayoutsSummary :total-payouts="payoutData?.total || 0"
                                            :payout-trend="payoutData?.trend || 0"
                                            :completed-payouts="payoutData?.completed || 0"
                                            :completed-amount="payoutData?.completedAmount || 0"
                                            :pending-payouts="payoutData?.pending || 0"
                                            :pending-amount="payoutData?.pendingAmount || 0"
                                            :scheduled-amount="payoutData?.scheduledAmount || 0"
                                            :is-loading="payoutStore.isLoading" />
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template #fallback>
                            <div class="w-full h-96 flex justify-center items-center">
                                <div class="flex flex-col items-center">
                                    <Loader2 class="animate-spin h-8 w-8 mb-2" />
                                    <p class="text-gray-500">Loading dashboard analytics...</p>
                                </div>
                            </div>
                        </template>
                    </Suspense>
                </div>

                <!-- Insights Sheet -->
                <Sheet :open="showInsights" @update:open="handleInsightsSheetChange">
                    <SheetContent :class="[
                        'h-full bg-white overflow-y-auto',
                        isMobile ? 'fixed inset-x-0 bottom-0 mt-24 rounded-t-[10px]' : 'sm:max-w-[900px] rounded-lg'
                    ]" :side="isMobile ? 'bottom' : 'right'">
                        <SheetHeader>
                            <SheetTitle>Business Insights & Analytics</SheetTitle>
                            <SheetDescription>
                                Real-time business analytics, performance metrics, and actionable insights
                            </SheetDescription>
                        </SheetHeader>
                        <div class="p-0">
                            <DashboardAnalyticsInsightsSidebar :booking-count="bookingCount"
                                :transactions-sum="transactionsSum" :route-count="routeCount"
                                :shuttle-count="shuttleCount" :today-bookings="todayBookings"
                                :available-drivers="availableDrivers" :is-sheet="true" @close="showInsights = false" />
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        </DashboardTheme>
    </NuxtLayout>
</template>
<script setup lang="ts">
import { Loader2, BarChart3, Plus, Home } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetTitle
} from '@/components/ui/sheet';
import { useRiderStore } from '~/stores/rider/rider.store';
import { useRouteStore } from '~/stores/route/route.store';
import { useShuttleStore } from '~/stores/shuttle/shuttle.store';
import { useDriverStore } from '~/stores/driver/driver.store';
import { useTransactionStore } from '~/stores/transaction/transaction.store';
import { useBookingStore } from '~/stores/booking/booking.store';

useHead({
    title: "Business Dashboard"
})

definePageMeta({
    middleware: "auth"
})

// Check if the device is mobile
const isMobile = ref(false)
onMounted(() => {
    isMobile.value = window.innerWidth < 768
    window.addEventListener('resize', () => {
        isMobile.value = window.innerWidth < 768
    })
})

// Insights functionality
const showInsights = ref(false);
const toggleInsights = () => {
    showInsights.value = !showInsights.value;
};

// Handle insights sheet open/close
const handleInsightsSheetChange = (isOpen: boolean) => {
    showInsights.value = isOpen;
};

// Get active shuttles count
const { pending: shuttlePending, data: shuttleCount } = useAsyncData<{ count: number, total: number }>(
    'activeShuttles',
    () => useShuttleStore().getActiveShuttlesCount(),
    { lazy: true }
)

// Get active routes count
const { pending: routePending, data: routeCount } = useAsyncData<{ count: number, total: number }>(
    'activeRoutes',
    () => useRouteStore().getActiveRoutesCount(),
    { lazy: true }
)

// Get today's bookings count
const { pending: todayBookingsPending, data: todayBookings } = useAsyncData<{ count: number, total: number }>(
    'todayBookings',
    () => useRiderStore().getTodayBookingsCount(),
    { lazy: true }
)

// Get available drivers count
const { pending: driversPending, data: availableDrivers } = useAsyncData<{ count: number, total: number }>(
    'availableDrivers',
    () => useDriverStore().getAvailableDriversCount(),
    { lazy: true }
)

// Get completed bookings count
const { pending: bookingPending, data: bookingCount } = useAsyncData<{ count: number, total: number }>(
    'completedBookings',
    () => useRiderStore().getCompletedRidesCount(),
    { lazy: true }
)

// Get transactions sum
const { pending: transactionsPending, data: transactionsSum } = useAsyncData<{ count: number, total: number }>(
    'transactionsSum',
    () => useTransactionStore().getTransactionsSum(),
    { lazy: true }
)

// Initialize stores for new components
const bookingStore = useBookingStore()
const customerStore = useRiderStore() // Using rider store for customers
const scheduleStore = useRouteStore() // Using route store for schedules

// Get recent bookings
const { data: recentBookings } = useAsyncData(
    'recentBookings',
    () => bookingStore.getBookings(5),
    { lazy: true, default: () => [] }
)

// Get recent customers
const { data: recentCustomers } = useAsyncData(
    'recentCustomers',
    () => customerStore.getRiders('recent'),
    { lazy: true, default: () => [] }
)

// Mock customer count data (replace with actual method when available)
const customerCount = ref({ total: 150, newThisMonth: 12 })

// Mock today's schedules (replace with actual method when available)
const todaySchedules = ref([
    {
        id: '1',
        departure_time: new Date().toISOString(),
        status: 'active',
        booked_seats: 8,
        route: { origin: 'Lagos', destination: 'Abuja', base_price: 15000 },
        shuttle: { name: 'Express 001', capacity: 14 }
    },
    {
        id: '2',
        departure_time: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
        status: 'scheduled',
        booked_seats: 5,
        route: { origin: 'Abuja', destination: 'Kano', base_price: 12000 },
        shuttle: { name: 'Comfort 002', capacity: 12 }
    }
])

// Mock payout data (replace with actual payout store when available)
const payoutData = ref({
    total: 125000,
    trend: 12.5,
    completed: 8,
    completedAmount: 95000,
    pending: 3,
    pendingAmount: 30000,
    scheduledAmount: 45000
})

// Mock payout store
const payoutStore = ref({ isLoading: false })
</script>