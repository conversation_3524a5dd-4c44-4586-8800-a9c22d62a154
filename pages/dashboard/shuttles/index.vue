<template>
    <ShuttlesTheme>
    <AnalyticsBasePageLayout
        title="Shuttle Fleet Management"
        subtitle="Comprehensive shuttle analytics and fleet management dashboard"
        new-button-text="Add Shuttle"
        table-title="Fleet Overview"
        :table-subtitle="`${filteredShuttles.length} of ${shuttleData?.length || 0} shuttles`"
        loading-text="Loading shuttle analytics..."
        details-sheet-title="Shuttle Details"
        details-sheet-description="View detailed information about this shuttle"
        insights-sheet-title="Fleet Insights & Analytics"
        insights-sheet-description="Real-time fleet analytics, alerts, and quick actions"
        @export="handleExport"
        ref="pageLayoutRef"
    >
        <!-- Header Actions -->
        <template #header-actions>
            <NewShuttleButton />
        </template>

        <!-- Metrics Cards -->
        <template #metrics-cards>
            <ShuttleAnalyticsMetricsCards :shuttles="shuttleData ?? []" />
        </template>

        <!-- Trends Chart -->
        <template #trends-chart>
            <ShuttleAnalyticsTrendsChart :shuttles="shuttleData ?? []" />
        </template>

        <!-- Filters -->
        <template #filters>
            <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
                    <Button variant="outline" size="sm" @click="clearFilters">
                        Clear All
                    </Button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <Input
                            v-model="searchQuery"
                            placeholder="Shuttle name, plate number..."
                            class="w-full"
                        />
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <Select v-model="statusFilter">
                            <SelectTrigger>
                                <SelectValue placeholder="All statuses" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All statuses</SelectItem>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                                <SelectItem value="maintenance">Maintenance</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <!-- Capacity Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Capacity</label>
                        <Select v-model="capacityFilter">
                            <SelectTrigger>
                                <SelectValue placeholder="All capacities" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All capacities</SelectItem>
                                <SelectItem value="small">Small (1-15)</SelectItem>
                                <SelectItem value="medium">Medium (16-30)</SelectItem>
                                <SelectItem value="large">Large (31+)</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <!-- Partner Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Partner</label>
                        <Select v-model="partnerFilter">
                            <SelectTrigger>
                                <SelectValue placeholder="All partners" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All partners</SelectItem>
                                <SelectItem v-for="partner in availablePartners" :key="partner" :value="partner">
                                    {{ partner }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>
        </template>

        <!-- Data Table -->
        <template #data-table>
            <DataTable
                :columns="columns"
                :data="filteredShuttles"
                :is-loading="false"
                @get-table-data="handleDataTableData"
                @get-row-data="handleRowClicked"
            >
                <template #dataTableSearch>
                    <Input v-if="dataTableRef" class="w-1/3" type="search"
                        placeholder="Quick search..."
                        @input="dataTableRef.setGlobalFilter($event.target.value)" />
                </template>
            </DataTable>
        </template>

        <!-- Details Content -->
        <template #details-content>
            <ShuttleInfo
                v-if="selectedShuttle"
                :shuttle="selectedShuttle"
                @close="pageLayoutRef?.closeDetailsSheet()"
            />
        </template>

        <!-- Insights Content -->
        <template #insights-content>
            <ShuttleAnalyticsInsightsSidebar
                :shuttles="shuttleData ?? []"
                :is-sheet="true"
                @close="pageLayoutRef?.closeInsightsSheet()"
            />
        </template>
    </AnalyticsBasePageLayout>
    </ShuttlesTheme>
</template>

<script setup lang="ts">
import { useShuttleStore } from '~/stores/shuttle/shuttle.store';
import type { ColumnDef, Row, Table } from '@tanstack/vue-table'
import { Checkbox } from '@/components/ui/checkbox'
import type { IShuttle } from '~/stores/shuttle/model/shuttle.model';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';

useHead({
    title: 'Shuttle Fleet Management'
})

definePageMeta({
    middleware: "auth"
})

const shuttleStore = useShuttleStore()

// Get the shuttles
const { data: shuttleData } = useAsyncData<IShuttle[]>(
    'shuttle',
    () => shuttleStore.getShuttles()
)

// Page layout reference
const pageLayoutRef = ref()

// Filter states
const searchQuery = ref('')
const statusFilter = ref('')
const capacityFilter = ref('')
const partnerFilter = ref('')

// Available partners (computed from shuttle data)
const availablePartners = computed(() => {
    if (!shuttleData.value) return [];
    const partners = new Set<string>();
    shuttleData.value.forEach(shuttle => {
        if (shuttle.partner_id) {
            partners.add(shuttle.partner_id);
        }
    });
    return Array.from(partners).sort();
});

// Filtered shuttles
const filteredShuttles = computed(() => {
    if (!shuttleData.value) return [];

    let filtered = [...shuttleData.value];

    // Apply search filter
    if (searchQuery.value) {
        const search = searchQuery.value.toLowerCase();
        filtered = filtered.filter(shuttle =>
            shuttle.name?.toLowerCase().includes(search) ||
            shuttle.plate_number?.toLowerCase().includes(search) ||
            shuttle.make?.toLowerCase().includes(search) ||
            shuttle.model?.toLowerCase().includes(search)
        );
    }

    // Apply status filter
    if (statusFilter.value) {
        filtered = filtered.filter(shuttle =>
            shuttle.status?.toLowerCase() === statusFilter.value.toLowerCase()
        );
    }

    // Apply capacity filter
    if (capacityFilter.value) {
        filtered = filtered.filter(shuttle => {
            const capacity = shuttle.capacity || 0;
            switch (capacityFilter.value) {
                case 'small': return capacity >= 1 && capacity <= 15;
                case 'medium': return capacity >= 16 && capacity <= 30;
                case 'large': return capacity >= 31;
                default: return true;
            }
        });
    }

    // Apply partner filter
    if (partnerFilter.value) {
        filtered = filtered.filter(shuttle =>
            shuttle.partner_id === partnerFilter.value
        );
    }

    return filtered;
});

// Clear all filters
const clearFilters = () => {
    searchQuery.value = '';
    statusFilter.value = '';
    capacityFilter.value = '';
    partnerFilter.value = '';
};

// Handle export
const handleExport = () => {
    shuttleStore.exportToCSV();
};

// Selected shuttle for details
const selectedShuttle = ref<IShuttle | null>(null);

// DATA TABLE STUFF
const dataTableRef = ref<Table<IShuttle>>()

const handleDataTableData = (data: Table<IShuttle>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<IShuttle>) => {
    // Set selected shuttle
    selectedShuttle.value = row.original;

    // Open details sheet
    pageLayoutRef.value?.openDetailsSheet();
}

const columns: ColumnDef<IShuttle>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllPageRowsSelected(),
            'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => row.toggleSelected(!!value),
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    // {
    //     accessorKey: 'id',
    //     header: 'Id',
    // },
    {
        accessorKey: 'created_at',
        header: 'Date Added',
        cell: ({ row }) => useFormatDateHuman(new Date(row.original.created_at)),
    },
    {
        accessorKey: 'name',
        header: 'Name',
    },
    {
        accessorKey: 'model',
        header: 'Model',
    },
    {
        accessorKey: 'make',
        header: 'Make',
    },
    {
        accessorKey: 'plate_number',
        header: 'Plate Number',
    },
    {
        accessorKey: 'capacity',
        header: 'Capacity',
    },
    {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => h('div', { class: 'flex space-x-2' }, [
            h(Badge, {
                variant: 'secondary',
                class: `${row.original.status === 'active' ? 'bg-green-400' : 'bg-gray-400'} text-black`
            }, row.original.status)
        ]),
    },
    // {
    //     id: 'amenities',
    //     header: 'Amenities',
    //     cell: ({ row }) => {
    //         const amenities = row.original.amenities;
    //         return h('div', { class: 'flex flex-wrap gap-1' },
    //             Object.entries(amenities)
    //                 .filter(([_, value]) => value === true)
    //                 .map(([key]) =>
    //                     h(Badge, { variant: 'outline', class: 'text-xs' },
    //                         key.replace('_', ' ')
    //                     )
    //                 )
    //         );
    //     }
    // },
    {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => h('button', {
            class: 'px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:underline',
            onClick: (e: Event) => {
                e.stopPropagation();
                selectedShuttle.value = row.original;
                pageLayoutRef.value?.openDetailsSheet();
            }
        }, 'View Details')
    }
]
// end of DATA TABLE STUFF
</script>