<template>
    <NuxtLayout name="dashboard">
        <div class="min-h-screen bg-transparent">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200">
                <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <div class="py-6 sm:flex sm:items-center sm:justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="p-3 bg-gray-100 rounded-xl">
                                <Bus class="h-8 w-8 text-gray-600" />
                            </div>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Shuttle Fleet Management</h1>
                                <p class="mt-1 text-sm text-gray-500">
                                    Comprehensive shuttle analytics and fleet management dashboard
                                </p>
                            </div>
                        </div>
                        <!-- <div class="flex items-center space-x-3 mt-4 sm:mt-0">
                            <Button variant="outline" @click="toggleInsights">
                                <BarChart3 class="h-4 w-4 mr-2" />
                                {{ showInsights ? 'Hide' : 'Show' }} Insights
                            </Button>
                            <Button>
                                <Plus class="h-4 w-4 mr-2" />
                                New Booking
                            </Button>
                        </div> -->
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8 py-6">
                <ShuttleAnalyticsMetricsCards :shuttles="shuttleData ?? []" />

                <!-- Enhanced Shuttle Table -->
                <div class="bg-white rounded-lg">
                    <div class="p-6 rounded-lg border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">All Shuttles</h3>
                                <p class="text-sm text-gray-500">
                                    {{ 0 }} of {{ 0 }}
                                    shuttles
                                </p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button variant="outline" size="sm">
                                    <Download class="h-4 w-4 mr-2" />
                                    Export
                                </Button>
                            </div>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <DataTable :columns="columns" :data="filteredShuttles" :is-loading="false"
                        @get-table-data="handleDataTableData" @get-row-data="handleRowClicked">
                        <template #dataTableSearch>
                            <Input v-if="dataTableRef" class="w-1/3" type="search" placeholder="Quick search..."
                                @input="dataTableRef.setGlobalFilter($event.target.value)" />
                        </template>
                    </DataTable>
                </div>

                <!-- Details Content -->
                <!-- <ShuttleInfo v-if="selectedShuttle" :shuttle="selectedShuttle"
                    @close="pageLayoutRef?.closeDetailsSheet()" /> -->



            </div>
        </div>
    </NuxtLayout>


</template>

<script setup lang="ts">
import { Bus, Download } from 'lucide-vue-next';
import { useShuttleStore } from '~/stores/shuttle/shuttle.store';
import type { ColumnDef, Row, Table } from '@tanstack/vue-table'
import { Checkbox } from '@/components/ui/checkbox'
import type { IShuttle } from '~/stores/shuttle/model/shuttle.model';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';

useHead({
    title: 'Shuttle Fleet Management'
})

definePageMeta({
    middleware: "auth"
})

const shuttleStore = useShuttleStore()

// Get the shuttles
const { data: shuttleData } = useAsyncData<IShuttle[]>(
    'shuttle',
    () => shuttleStore.getShuttles()
)

// Page layout reference
const pageLayoutRef = ref()

// Filter states
const searchQuery = ref('')
const statusFilter = ref('')
const capacityFilter = ref('')
const partnerFilter = ref('')

// Available partners (computed from shuttle data)
const availablePartners = computed(() => {
    if (!shuttleData.value) return [];
    const partners = new Set<string>();
    shuttleData.value.forEach(shuttle => {
        if (shuttle.partner_id) {
            partners.add(shuttle.partner_id);
        }
    });
    return Array.from(partners).sort();
});

// Filtered shuttles
const filteredShuttles = computed(() => {
    if (!shuttleData.value) return [];

    let filtered = [...shuttleData.value];

    // Apply search filter
    if (searchQuery.value) {
        const search = searchQuery.value.toLowerCase();
        filtered = filtered.filter(shuttle =>
            shuttle.name?.toLowerCase().includes(search) ||
            shuttle.plate_number?.toLowerCase().includes(search) ||
            shuttle.make?.toLowerCase().includes(search) ||
            shuttle.model?.toLowerCase().includes(search)
        );
    }

    // Apply status filter
    if (statusFilter.value) {
        filtered = filtered.filter(shuttle =>
            shuttle.status?.toLowerCase() === statusFilter.value.toLowerCase()
        );
    }

    // Apply capacity filter
    if (capacityFilter.value) {
        filtered = filtered.filter(shuttle => {
            const capacity = shuttle.capacity || 0;
            switch (capacityFilter.value) {
                case 'small': return capacity >= 1 && capacity <= 15;
                case 'medium': return capacity >= 16 && capacity <= 30;
                case 'large': return capacity >= 31;
                default: return true;
            }
        });
    }

    // Apply partner filter
    if (partnerFilter.value) {
        filtered = filtered.filter(shuttle =>
            shuttle.partner_id === partnerFilter.value
        );
    }

    return filtered;
});

// Clear all filters
const clearFilters = () => {
    searchQuery.value = '';
    statusFilter.value = '';
    capacityFilter.value = '';
    partnerFilter.value = '';
};

// Handle export
const handleExport = () => {
    shuttleStore.exportToCSV();
};

// Selected shuttle for details
const selectedShuttle = ref<IShuttle | null>(null);

// DATA TABLE STUFF
const dataTableRef = ref<Table<IShuttle>>()

const handleDataTableData = (data: Table<IShuttle>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<IShuttle>) => {
    // Set selected shuttle
    selectedShuttle.value = row.original;

    // Open details sheet
    pageLayoutRef.value?.openDetailsSheet();
}

const columns: ColumnDef<IShuttle>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllPageRowsSelected(),
            'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => row.toggleSelected(!!value),
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    // {
    //     accessorKey: 'id',
    //     header: 'Id',
    // },
    {
        accessorKey: 'created_at',
        header: 'Date Added',
        cell: ({ row }) => useFormatDateHuman(new Date(row.original.created_at)),
    },
    {
        accessorKey: 'name',
        header: 'Name',
    },
    {
        accessorKey: 'model',
        header: 'Model',
    },
    {
        accessorKey: 'make',
        header: 'Make',
    },
    {
        accessorKey: 'plate_number',
        header: 'Plate Number',
    },
    {
        accessorKey: 'capacity',
        header: 'Capacity',
    },
    {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => h('div', { class: 'flex space-x-2' }, [
            h(Badge, {
                variant: 'secondary',
                class: `${row.original.status === 'active' ? 'bg-green-400' : 'bg-gray-400'} text-black`
            }, row.original.status)
        ]),
    },
    // {
    //     id: 'amenities',
    //     header: 'Amenities',
    //     cell: ({ row }) => {
    //         const amenities = row.original.amenities;
    //         return h('div', { class: 'flex flex-wrap gap-1' },
    //             Object.entries(amenities)
    //                 .filter(([_, value]) => value === true)
    //                 .map(([key]) =>
    //                     h(Badge, { variant: 'outline', class: 'text-xs' },
    //                         key.replace('_', ' ')
    //                     )
    //                 )
    //         );
    //     }
    // },
    {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => h('button', {
            class: 'px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:underline',
            onClick: (e: Event) => {
                e.stopPropagation();
                selectedShuttle.value = row.original;
                pageLayoutRef.value?.openDetailsSheet();
            }
        }, 'View Details')
    }
]
// end of DATA TABLE STUFF
</script>