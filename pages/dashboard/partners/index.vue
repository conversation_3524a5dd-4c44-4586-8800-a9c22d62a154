<template>
    <PartnersTheme>
    <AnalyticsBasePageLayout
        title="Partner Management & Analytics"
        subtitle="Comprehensive partner analytics, revenue insights, and partnership performance metrics"
        new-button-text="Add Partner"
        table-title="Partner Overview"
        :table-subtitle="`${filteredPartners.length} of ${partnersData?.length || 0} partners`"
        loading-text="Loading partner analytics..."
        details-sheet-title="Partner Details"
        details-sheet-description="View detailed information about this partner"
        insights-sheet-title="Partner Insights & Analytics"
        insights-sheet-description="Partnership analytics, revenue insights, and commission performance metrics"
        @export="handleExport"
        ref="pageLayoutRef"
    >
        <!-- Header Actions -->
        <template #header-actions>
            <PartnerNewButton />
        </template>

        <!-- Metrics Cards -->
        <template #metrics-cards>
            <PartnerAnalyticsMetricsCards :partners="partnersData ?? []" />
        </template>

        <!-- Trends Chart -->
        <template #trends-chart>
            <PartnerAnalyticsTrendsChart :partners="partnersData ?? []" />
        </template>

        <!-- Filters -->
        <template #filters>
            <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Partner Filters</h3>
                    <Button variant="outline" size="sm" @click="clearFilters">
                        Clear All
                    </Button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <Input
                            v-model="searchQuery"
                            placeholder="Partner name, email..."
                            class="w-full"
                        />
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <Select v-model="statusFilter">
                            <SelectTrigger>
                                <SelectValue placeholder="All statuses" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All statuses</SelectItem>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                                <SelectItem value="suspended">Suspended</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <!-- Commission Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Commission Range</label>
                        <Select v-model="commissionFilter">
                            <SelectTrigger>
                                <SelectValue placeholder="All ranges" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All ranges</SelectItem>
                                <SelectItem value="low">Low (< 5%)</SelectItem>
                                <SelectItem value="medium">Medium (5-15%)</SelectItem>
                                <SelectItem value="high">High (> 15%)</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <!-- Performance Level -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Performance</label>
                        <Select v-model="performanceFilter">
                            <SelectTrigger>
                                <SelectValue placeholder="All levels" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All levels</SelectItem>
                                <SelectItem value="high">High Performer</SelectItem>
                                <SelectItem value="medium">Medium Performer</SelectItem>
                                <SelectItem value="low">Low Performer</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>
        </template>

        <!-- Data Table -->
        <template #data-table>
            <div v-if="partnersPending" class="w-full h-60 flex justify-center items-center">
                <Loader2 class="animate-spin h-8 w-8" />
            </div>
            <template v-else-if="partnersData && partnersData.length > 0">
                <DataTable
                    :columns="columns"
                    :data="filteredPartners"
                    :is-loading="partnersPending"
                    @get-table-data="handleDataTableData"
                    @get-row-data="handleRowClicked"
                >
                    <template #dataTableSearch>
                        <Input v-if="dataTableRef" class="w-1/3" type="search"
                            placeholder="Quick search..."
                            @input="dataTableRef.setGlobalFilter($event.target.value)" />
                    </template>
                            <!-- <template #dataTableFacetedFilter>
                            <div class="text-right">
                                <p class="text-muted-foreground text-sm mb-2">Filter By</p>
                                <div class="flex items-center gap-2">

                                   <p>Faceted filter</p>

                                </div>
                            </div>

                        </template> -->
                </DataTable>
            </template>
            <div v-else class="w-full h-60 flex justify-center items-center">
                <div class="text-center">
                    <Building2 class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-500">No partners found</p>
                </div>
            </div>
        </template>

        <!-- Details Content -->
        <template #details-content>
            <PartnerInfo
                v-if="selectedPartner"
                :partner="selectedPartner"
                @close="pageLayoutRef?.closeDetailsSheet()"
            />
        </template>

        <!-- Insights Content -->
        <template #insights-content>
            <PartnerAnalyticsInsightsSidebar
                :partners="partnersData ?? []"
                :is-sheet="true"
                @close="pageLayoutRef?.closeInsightsSheet()"
            />
        </template>
    </AnalyticsBasePageLayout>
    </PartnersTheme>
</template>

<script setup lang="ts">
import type { ColumnDef, Row, Table } from '@tanstack/vue-table'
import { Checkbox } from '@/components/ui/checkbox'
import { usePartnerStore } from '~/stores/partner/partner.store';
import UserFullAvatar from '@/components/UserFullAvatar.vue'
import type { IPartner } from '~/stores/partner/model/partner.model';
import { Loader2, Building2 } from 'lucide-vue-next';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';

useHead({
    title: 'Partner Management & Analytics'
})

definePageMeta({
    middleware: "auth"
})

const partnerStore = usePartnerStore()

// Get the partners
const {pending:partnersPending, status:partnersStatus, data:partnersData, error} = useAsyncData<IPartner[]>('partners', ()=> partnerStore.getPartners(),{lazy : true})

// Page layout reference
const pageLayoutRef = ref()

// Filter states
const searchQuery = ref('')
const statusFilter = ref('')
const commissionFilter = ref('')
const performanceFilter = ref('')

// Filtered partners
const filteredPartners = computed(() => {
    if (!partnersData.value) return [];

    let filtered = [...partnersData.value];

    // Apply search filter
    if (searchQuery.value) {
        const search = searchQuery.value.toLowerCase();
        filtered = filtered.filter(partner =>
            partner.name?.toLowerCase().includes(search) ||
            partner.email?.toLowerCase().includes(search) ||
            partner.phone?.toLowerCase().includes(search) ||
            partner.address?.toLowerCase().includes(search)
        );
    }

    // Apply status filter
    if (statusFilter.value) {
        filtered = filtered.filter(partner =>
            partner.status?.toLowerCase() === statusFilter.value.toLowerCase()
        );
    }

    // Apply commission filter
    if (commissionFilter.value) {
        filtered = filtered.filter(partner => {
            const commission = partner.commission_rate || 0;
            switch (commissionFilter.value) {
                case 'low': return commission < 5;
                case 'medium': return commission >= 5 && commission <= 15;
                case 'high': return commission > 15;
                default: return true;
            }
        });
    }

    return filtered;
});

// Clear all filters
const clearFilters = () => {
    searchQuery.value = '';
    statusFilter.value = '';
    commissionFilter.value = '';
    performanceFilter.value = '';
};

// Handle export
const handleExport = () => {
    // Implement partner export functionality
    console.log('Exporting partner data...');
};

// Selected partner for details
const selectedPartner = ref<IPartner | null>(null);


// SHEET CONTROL
const isSheetDialogueOpen = ref(false)
const handleOnSheetDialogOpen = (isOpen: boolean) => {
    isSheetDialogueOpen.value = isOpen
}
// end of SHEET CONTROL

// DATA TABLE STUFF
const selectedRow = ref<Row<IPartner>>()
const dataTableRef = ref<Table<IPartner>>()

const handleDataTableData = (data: Table<IPartner>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<IPartner>) => {
    // Set selected partner
    selectedPartner.value = row.original;

    // Open details sheet
    pageLayoutRef.value?.openDetailsSheet();
}


const columns: ColumnDef<IPartner>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllPageRowsSelected(),
            'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => row.toggleSelected(!!value),
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'id',
        header: 'Id',
    },
    {
        accessorKey: 'created_at',
        header: 'Date Added',
        cell: ({ row }) => useFormatDateHuman(new Date(row.original.created_at)),
    },
    {
        header: 'Name',
        cell: ({ row }) => h(UserFullAvatar, {
            firstname: row.original.name ?? '',
            lastname: '',
            avatarUrl: row.original.avatar_url
        }),
    },
    {
        accessorKey: 'email',
        header: 'Email',
    },
    {
        accessorKey: 'phone',
        header: 'Phone',
        cell: ({ row }) => row.original.phone || 'N/A',
    },
    {
        accessorKey: 'commission_rate',
        header: 'Commission',
        cell: ({ row }) => `${row.original.commission_rate}%`,
    },
    {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => h('div', { class: 'flex space-x-2' }, [
            h(Badge, {
                variant: 'secondary',
                class: row.original.status === 'active' ? 'bg-green-400 text-black' : 'bg-gray-400 text-black'
            }, row.original.status),
        ]),
    }
]
// end of DATA TABLE STUFF

</script>