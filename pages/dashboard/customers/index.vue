<template>
    <CustomersTheme>
        <AnalyticsBasePageLayout title="Customer Management & Analytics"
            subtitle="Comprehensive customer analytics, behavior insights, and engagement metrics"
            new-button-text="Invite Customer" table-title="Customer Overview"
            :table-subtitle="`${filteredCustomers.length} of ${ridersData?.length || 0} customers`"
            loading-text="Loading customer analytics..." details-sheet-title="Customer Details"
            details-sheet-description="View detailed information about this customer"
            insights-sheet-title="Customer Insights & Analytics"
            insights-sheet-description="Customer behavior analytics, retention insights, and engagement metrics"
            @export="handleExport" ref="pageLayoutRef">
            <!-- Header Actions -->
            <template #header-actions>
                <Button variant="outline">
                    <UserPlus class="h-4 w-4 mr-2" />
                    Invite Customer
                </Button>
            </template>

            <!-- Metrics Cards -->
            <template #metrics-cards>
                <CustomerAnalyticsMetricsCards :customers="ridersData ?? []" />
            </template>

            <!-- Trends Chart -->
            <template #trends-chart>
                <CustomerAnalyticsTrendsChart :customers="ridersData ?? []" />
            </template>

            <!-- Filters -->
            <template #filters>
                <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Customer Filters</h3>
                        <Button variant="outline" size="sm" @click="clearFilters">
                            Clear All
                        </Button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <!-- Search -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                            <Input v-model="searchQuery" placeholder="Name, email, phone..." class="w-full" />
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <Select v-model="statusFilter">
                                <SelectTrigger>
                                    <SelectValue placeholder="All statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All statuses</SelectItem>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="inactive">Inactive</SelectItem>
                                    <SelectItem value="suspended">Suspended</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <!-- Registration Period -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Registration</label>
                            <Select v-model="registrationFilter">
                                <SelectTrigger>
                                    <SelectValue placeholder="All periods" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All periods</SelectItem>
                                    <SelectItem value="this_week">This Week</SelectItem>
                                    <SelectItem value="this_month">This Month</SelectItem>
                                    <SelectItem value="last_month">Last Month</SelectItem>
                                    <SelectItem value="older">Older</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <!-- Engagement Level -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Engagement</label>
                            <Select v-model="engagementFilter">
                                <SelectTrigger>
                                    <SelectValue placeholder="All levels" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All levels</SelectItem>
                                    <SelectItem value="high">High Engagement</SelectItem>
                                    <SelectItem value="medium">Medium Engagement</SelectItem>
                                    <SelectItem value="low">Low Engagement</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </div>
            </template>

            <!-- Data Table -->
            <template #data-table>
                <div v-if="isLoading" class="w-full h-60 flex justify-center items-center">
                    <Loader2 class="animate-spin h-8 w-8" />
                </div>
                <template v-else-if="data && data.length > 0">
                    <DataTable :columns="columns" :data="filteredCustomers" :is-loading="isLoading"
                        @get-table-data="handleDataTableData" @get-row-data="handleRowClicked">
                        <template #dataTableSearch>
                            <Input v-if="dataTableRef" class="w-1/3" type="search" placeholder="Quick search..."
                                @input="dataTableRef.setGlobalFilter($event.target.value)" />
                        </template>
                    </DataTable>
                </template>
                <div v-else class="w-full h-60 flex justify-center items-center">
                    <div class="text-center">
                        <Users class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p class="text-gray-500">No customers found</p>
                    </div>
                </div>
            </template>

            <!-- Details Content -->
            <template #details-content>
                <CustomerInfo v-if="selectedCustomer" :customer="selectedCustomer"
                    @close="pageLayoutRef?.closeDetailsSheet()" />
            </template>

            <!-- Insights Content -->
            <template #insights-content>
                <CustomerAnalyticsInsightsSidebar :customers="data ?? []" :is-sheet="true"
                    @close="pageLayoutRef?.closeInsightsSheet()" />
            </template>
        </AnalyticsBasePageLayout>
    </CustomersTheme>
</template>

<script setup lang="ts">
import { useRiderStore } from '~/stores/rider/rider.store';
import type { User } from '~/stores/auth/user/model/user.model';
import { Loader2, Users, UserPlus } from 'lucide-vue-next';
import type { ColumnDef, Row, Table } from '@tanstack/vue-table'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';
import UserFullAvatar from '@/components/UserFullAvatar.vue'

useHead({
    title: 'Customer Management & Analytics'
})


definePageMeta({
    middleware: "auth"
})

const riderStore = useRiderStore()

// Get the riders
const ridersData = await useAsyncData<User[]>('riders', () => riderStore.getRiders(), { lazy: true })

// Extract data for easier access
const data = computed(() => ridersData.data.value || [])
const isLoading = computed(() => riderStore.isLoading)

// Page layout reference
const pageLayoutRef = ref()

// Filter states
const searchQuery = ref('')
const statusFilter = ref('')
const registrationFilter = ref('')
const engagementFilter = ref('')

// Filtered customers
const filteredCustomers = computed(() => {
    if (!data.value) return [];

    let filtered = [...data.value];

    // Apply search filter
    if (searchQuery.value) {
        const search = searchQuery.value.toLowerCase();
        filtered = filtered.filter(customer =>
            customer.first_name?.toLowerCase().includes(search) ||
            customer.last_name?.toLowerCase().includes(search) ||
            customer.email?.toLowerCase().includes(search) ||
            customer.phone_number?.toLowerCase().includes(search)
        );
    }

    // Apply status filter (mock logic based on role)
    if (statusFilter.value) {
        filtered = filtered.filter(customer => {
            const isActive = customer.role?.name?.toLowerCase() === 'rider';
            switch (statusFilter.value) {
                case 'active': return isActive;
                case 'inactive': return !isActive;
                case 'suspended': return false; // Mock - no suspended users in current data
                default: return true;
            }
        });
    }

    return filtered;
});

// Clear all filters
const clearFilters = () => {
    searchQuery.value = '';
    statusFilter.value = '';
    registrationFilter.value = '';
    engagementFilter.value = '';
};

// Handle export
const handleExport = () => {
    // Implement customer export functionality
    console.log('Exporting customer data...');
};

// Selected customer for details
const selectedCustomer = ref<User | null>(null);

// DATA TABLE STUFF
const selectedRow = ref<Row<User>>()
const dataTableRef = ref<Table<User>>()

const handleDataTableData = (data: Table<User>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<User>) => {
    // Set selected customer
    selectedCustomer.value = row.original;

    // Open details sheet
    pageLayoutRef.value?.openDetailsSheet();
}


const columns: ColumnDef<User>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllPageRowsSelected(),
            'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => row.toggleSelected(!!value),
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'id',
        header: 'Id',
    },
    {
        accessorKey: 'created_at',
        header: 'Date Added',
        cell: ({ row }) => row.original.created_at ? useFormatDateHuman(new Date(row.original.created_at)) : 'N/A',
    },
    {
        header: 'Name',
        cell: ({ row }) => h(UserFullAvatar, {
            firstname: row.original.first_name ?? '',
            lastname: row.original.last_name ?? '',
            avatarUrl: row.original.avatar_url ?? '',
        }),
    },
    {
        accessorKey: 'email',
        header: 'Email',
    },
    {
        accessorKey: 'phone_number',
        header: 'Phone',
        cell: ({ row }) => `${row.original.country_code || ''} ${row.original.phone_number || ''}`,
    },
    {
        header: 'Role',
        cell: ({ row }) => row.original.role?.name || 'Rider',
    },

]
// end of DATA TABLE STUFF


</script>