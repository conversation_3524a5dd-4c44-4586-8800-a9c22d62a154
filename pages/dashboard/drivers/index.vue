<template>
    <NuxtLayout name="dashboard">
        <DriversTheme>
        <div class="py-12 bg-transparent sm:py-16 lg:py-20">
            <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="sm:flex sm:items-center sm:justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Driver Management</h1>
                        <p class="mt-1 text-sm text-gray-500">
                            Comprehensive driver analytics and management dashboard
                        </p>
                    </div>
                    <div class="flex items-center space-x-3 mt-4 sm:mt-0">
                        <Button variant="outline" @click="toggleInsights">
                            <BarChart3 class="h-4 w-4 mr-2" />
                            {{ showInsights ? 'Hide' : 'Show' }} Insights
                        </Button>
                        <div @click="navigateTo('https://play.google.com/store/apps/details?id=com.kwansoshuttle.driver', {external : true})">
                            <DriverNewButton></DriverNewButton>
                        </div>
                    </div>
                </div>

                <!-- DRIVER DATA -->
                <Suspense>
                    <template #default>
                        <div class="mt-6">
                            <DataTable
                                :columns="columns"
                                :data="driversData ?? []"
                                :is-loading="false"
                                @get-table-data="handleDataTableData"
                                @get-row-data="handleRowClicked"
                            >
                                <template #dataTableSearch>
                                    <Input v-if="dataTableRef" class="w-1/3" type="search"
                                        placeholder="Search drivers..."
                                        @input="dataTableRef.setGlobalFilter($event.target.value)" />
                                </template>
                            </DataTable>
                        </div>
                    </template>
                    <template #fallback>
                        <div class="w-full h-60 flex justify-center items-center">
                            <div class="flex flex-col items-center">
                                <Loader2 class="animate-spin h-8 w-8 mb-2" />
                                <p class="text-gray-500">Loading drivers...</p>
                            </div>
                        </div>
                    </template>
                </Suspense>
                <!-- end of DRIVER DATA -->

                <!-- Driver Details Sheet -->
                <Sheet :open="isSheetOpen" @update:open="handleSheetOpen">
                    <SheetContent
                        :class="[
                            'h-full bg-white overflow-y-auto',
                            isMobile ? 'fixed inset-x-0 bottom-0 mt-24 rounded-t-[10px]' : 'sm:max-w-[900px] rounded-lg',
                        ]"
                        :side="isMobile ? 'bottom' : 'right'"
                    >
                        <div v-if="selectedRow?.original">
                            <Suspense>
                                <template #default>
                                    <DriverInfo :driver="selectedRow.original" />
                                </template>
                                <template #fallback>
                                    <div class="w-full h-40 flex justify-center items-center">
                                        <Loader2 class="animate-spin" />
                                    </div>
                                </template>
                            </Suspense>
                        </div>
                        <SheetFooter>
                            <Button @click="isSheetOpen = false">Close</Button>
                        </SheetFooter>
                    </SheetContent>
                </Sheet>

                <!-- Driver Insights Sheet -->
                <Sheet :open="showInsights" @update:open="handleInsightsSheetChange">
                    <SheetContent
                        :class="[
                            'h-full bg-white overflow-y-auto',
                            isMobile ? 'fixed inset-x-0 bottom-0 mt-24 rounded-t-[10px]' : 'sm:max-w-[900px] rounded-lg'
                        ]"
                        :side="isMobile ? 'bottom' : 'right'"
                    >
                        <SheetHeader>
                            <SheetTitle>Driver Insights & Analytics</SheetTitle>
                            <SheetDescription>
                                Real-time driver analytics, performance metrics, and quick actions
                            </SheetDescription>
                        </SheetHeader>
                        <div class="p-4">
                            <DriverAnalyticsInsightsSidebar
                                :drivers="driversData ?? []"
                                :is-sheet="true"
                                @close="showInsights = false"
                            />
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        </div>
        </DriversTheme>
    </NuxtLayout>
</template>

<script setup lang="ts">
import type { ColumnDef, Row, Table } from '@tanstack/vue-table'
import { Checkbox } from '@/components/ui/checkbox'
import UserFullAvatar from '@/components/UserFullAvatar.vue'
import { useDriverStore } from '~/stores/driver/driver.store';
import type { User } from '~/stores/auth/user/model/user.model';
import { Loader2, BarChart3 } from 'lucide-vue-next';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetFooter,
    SheetHeader,
    SheetTitle
} from '@/components/ui/sheet';

useHead({
    title: 'Drivers'
})

definePageMeta({
    middleware: "auth"
})

const driverStore = useDriverStore()

// Check if the device is mobile
const isMobile = ref(false)
onMounted(() => {
    isMobile.value = window.innerWidth < 768
    window.addEventListener('resize', () => {
        isMobile.value = window.innerWidth < 768
    })
})

// Get the drivers - using Suspense, so we don't need lazy loading
const { data: driversData } = useAsyncData<User[]>(
    'drivers',
    () => driverStore.getDrivers()
)

// Insights functionality
const showInsights = ref(false);
const toggleInsights = () => {
    showInsights.value = !showInsights.value;
};

// Handle insights sheet open/close
const handleInsightsSheetChange = (isOpen: boolean) => {
    showInsights.value = isOpen;
};

// DATA TABLE STUFF
const selectedRow = ref<Row<User>>()
const dataTableRef = ref<Table<User>>()

const handleDataTableData = (data: Table<User>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<User>) => {
    // Set selected row
    selectedRow.value = row;

    // Open Sheet
    isSheetOpen.value = true;
}

// SHEET CONTROL
const isSheetOpen = ref(false);
const handleSheetOpen = (isOpen: boolean) => {
    isSheetOpen.value = isOpen;
    if (!isOpen) {
        // Clear selected row when sheet is closed
        selectedRow.value = undefined;
    }
}


const columns: ColumnDef<User>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllPageRowsSelected(),
            'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => row.toggleSelected(!!value),
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'created_at',
        header: 'Date Added',
        cell: () => 'N/A',
    },
    {
        header: 'Name',
        cell: ({ row }) => h(UserFullAvatar, {
            firstname: row.original.first_name ?? '',
            lastname: row.original.last_name ?? '',
            avatarUrl: row.original.avatar_url,
        }),
    },
    {
        accessorKey: 'email',
        header: 'Email',
    },
    {
        accessorKey: 'phone_number',
        header: 'Phone',
        cell: ({ row }) => `${row.original.country_code || ''} ${row.original.phone_number || ''}`,
    },
    {
        header: 'Role',
        cell: ({ row }) => row.original.role?.name || 'Driver',
    },
    {
        header: 'Assigned Shuttle',
        cell: () => 'Not Assigned', // Placeholder for now
    },
    {
        header: 'Partner',
        cell: () => 'N/A', // Placeholder for now
    },
    {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => h('button', {
            class: 'px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:underline',
            onClick: (e: Event) => {
                e.stopPropagation();
                selectedRow.value = row;
                isSheetOpen.value = true;
            }
        }, 'View Details')
    }
]
// end of DATA TABLE STUFF

</script>