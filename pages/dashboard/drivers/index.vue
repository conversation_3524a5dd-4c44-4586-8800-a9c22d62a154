<template>
    <NuxtLayout name="dashboard">
        <div class="min-h-screen bg-transparent">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200">
                <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <div class="py-6 sm:flex sm:items-center sm:justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="p-3 bg-gray-100 rounded-xl">
                                <BusFront class="h-8 w-8 text-gray-600" />
                            </div>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Driver Management</h1>
                                <p class="mt-1 text-sm text-gray-500">
                                    Comprehensive driver analytics and management dashboard
                                </p>
                            </div>
                        </div>
                        <!-- <div class="flex items-center space-x-3 mt-4 sm:mt-0">
                            <Button variant="outline" @click="toggleInsights">
                                <BarChart3 class="h-4 w-4 mr-2" />
                                {{ showInsights ? 'Hide' : 'Show' }} Insights
                            </Button>
                            <Button>
                                <Plus class="h-4 w-4 mr-2" />
                                New Booking
                            </Button>
                        </div> -->
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8 py-6">
                <!-- Analytics Dashboard -->
                <Suspense>
                    <template #default>
                        <div class="space-y-6">
                            <!-- Key Metrics Cards -->
                            <!-- <BookingAnalyticsMetricsCards :bookings="bookingsData ?? []" /> -->

                            <!-- Trends Chart -->
                            <!-- <BookingAnalyticsTrendsChart :bookings="bookingsData ?? []" /> -->

                            <!-- Filters -->
                            <!-- <BookingFilters
                                :bookings="bookingsData ?? []"
                                @filters-changed="handleFiltersChanged"
                            /> -->

                            <!-- Enhanced Booking Table -->
                            <div class="bg-white rounded-lg">
                                <div class="p-6 rounded-lg border border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-900">All Drivers</h3>
                                            <p class="text-sm text-gray-500">
                                                {{ 0 }} of {{ 0 }}
                                                drivers
                                            </p>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <Button variant="outline" size="sm">
                                                <Download class="h-4 w-4 mr-2" />
                                                Export
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-6">
                                    <DataTable :columns="columns" :data="driversData ?? []" :is-loading="false"
                                        @get-table-data="handleDataTableData" @get-row-data="handleRowClicked">
                                        <template #dataTableSearch>
                                            <Input v-if="dataTableRef" class="w-1/3" type="search"
                                                placeholder="Search drivers..."
                                                @input="dataTableRef.setGlobalFilter($event.target.value)" />
                                        </template>
                                    </DataTable>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #fallback>
                        <div class="w-full h-96 flex justify-center items-center">
                            <div class="flex flex-col items-center">
                                <Loader2 class="animate-spin h-8 w-8 mb-2" />
                                <p class="text-gray-500">Loading drivers...</p>
                            </div>
                        </div>
                    </template>
                </Suspense>
            </div>

            <!-- Driver Details Sheet -->
            <Sheet :open="isSheetOpen" @update:open="handleSheetOpen">
                <SheetContent :class="[
                    'h-full bg-white overflow-y-auto',
                    isMobile ? 'fixed inset-x-0 bottom-0 mt-24 rounded-t-[10px]' : 'sm:max-w-[900px] rounded-lg',
                ]" :side="isMobile ? 'bottom' : 'right'">
                    <div v-if="selectedRow?.original">
                        <Suspense>
                            <template #default>
                                <DriverInfo :driver="selectedRow.original" />
                            </template>
                            <template #fallback>
                                <div class="w-full h-40 flex justify-center items-center">
                                    <Loader2 class="animate-spin" />
                                </div>
                            </template>
                        </Suspense>
                    </div>
                    <SheetFooter>
                        <Button @click="isSheetOpen = false">Close</Button>
                    </SheetFooter>
                </SheetContent>
            </Sheet>


        </div>
    </NuxtLayout>


  
</template>

<script setup lang="ts">
import type { ColumnDef, Row, Table } from '@tanstack/vue-table'
import { Checkbox } from '@/components/ui/checkbox'
import UserFullAvatar from '@/components/UserFullAvatar.vue'
import { useDriverStore } from '~/stores/driver/driver.store';
import type { User } from '~/stores/auth/user/model/user.model';
import { Loader2, BusFront } from 'lucide-vue-next';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetFooter,
    SheetHeader,
    SheetTitle
} from '@/components/ui/sheet';

useHead({
    title: 'Drivers'
})

definePageMeta({
    middleware: "auth"
})

const driverStore = useDriverStore()

// Check if the device is mobile
const isMobile = ref(false)
onMounted(() => {
    isMobile.value = window.innerWidth < 768
    window.addEventListener('resize', () => {
        isMobile.value = window.innerWidth < 768
    })
})

// Get the drivers - using Suspense, so we don't need lazy loading
const { data: driversData } = useAsyncData<User[]>(
    'drivers',
    () => driverStore.getDrivers()
)

// Insights functionality
const showInsights = ref(false);
const toggleInsights = () => {
    showInsights.value = !showInsights.value;
};

// Handle insights sheet open/close
const handleInsightsSheetChange = (isOpen: boolean) => {
    showInsights.value = isOpen;
};

// DATA TABLE STUFF
const selectedRow = ref<Row<User>>()
const dataTableRef = ref<Table<User>>()

const handleDataTableData = (data: Table<User>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<User>) => {
    // Set selected row
    selectedRow.value = row;

    // Open Sheet
    isSheetOpen.value = true;
}

// SHEET CONTROL
const isSheetOpen = ref(false);
const handleSheetOpen = (isOpen: boolean) => {
    isSheetOpen.value = isOpen;
    if (!isOpen) {
        // Clear selected row when sheet is closed
        selectedRow.value = undefined;
    }
}


const columns: ColumnDef<User>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllPageRowsSelected(),
            'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => row.toggleSelected(!!value),
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'created_at',
        header: 'Date Added',
        cell: () => 'N/A',
    },
    {
        header: 'Name',
        cell: ({ row }) => h(UserFullAvatar, {
            firstname: row.original.first_name ?? '',
            lastname: row.original.last_name ?? '',
            avatarUrl: row.original.avatar_url,
        }),
    },
    {
        accessorKey: 'email',
        header: 'Email',
    },
    {
        accessorKey: 'phone_number',
        header: 'Phone',
        cell: ({ row }) => `${row.original.country_code || ''} ${row.original.phone_number || ''}`,
    },
    {
        header: 'Role',
        cell: ({ row }) => row.original.role?.name || 'Driver',
    },
    {
        header: 'Assigned Shuttle',
        cell: () => 'Not Assigned', // Placeholder for now
    },
    {
        header: 'Partner',
        cell: () => 'N/A', // Placeholder for now
    },
    {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => h('button', {
            class: 'px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:underline',
            onClick: (e: Event) => {
                e.stopPropagation();
                selectedRow.value = row;
                isSheetOpen.value = true;
            }
        }, 'View Details')
    }
]
// end of DATA TABLE STUFF

</script>