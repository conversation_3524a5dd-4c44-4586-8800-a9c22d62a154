<template>
    <NuxtLayout name="dashboard">
        <BookingsTheme>
        <div class="min-h-screen bg-transparent">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200">
                <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <div class="py-6 sm:flex sm:items-center sm:justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="p-3 bg-gray-100 rounded-xl">
                                <Calendar class="h-8 w-8 text-gray-600" />
                            </div>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Bookings Management</h1>
                                <p class="mt-1 text-sm text-gray-500">
                                    Comprehensive booking analytics and management dashboard
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 mt-4 sm:mt-0">
                            <Button variant="outline" @click="toggleInsights">
                                <BarChart3 class="h-4 w-4 mr-2" />
                                {{ showInsights ? 'Hide' : 'Show' }} Insights
                            </Button>
                            <Button>
                                <Plus class="h-4 w-4 mr-2" />
                                New Booking
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8 py-6">
                <!-- Analytics Dashboard -->
                <Suspense>
                    <template #default>
                        <div class="space-y-6">
                            <!-- Key Metrics Cards -->
                            <BookingAnalyticsMetricsCards :bookings="bookingsData ?? []" />

                            <!-- Trends Chart -->
                            <BookingAnalyticsTrendsChart :bookings="bookingsData ?? []" />

                            <!-- Filters -->
                            <BookingFilters
                                :bookings="bookingsData ?? []"
                                @filters-changed="handleFiltersChanged"
                            />

                            <!-- Enhanced Booking Table -->
                            <div class="bg-white rounded-lg border border-gray-200">
                                <div class="p-6 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-900">All Bookings</h3>
                                            <p class="text-sm text-gray-500">
                                                {{ filteredBookings.length }} of {{ bookingsData?.length || 0 }} bookings
                                            </p>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <Button variant="outline" size="sm">
                                                <Download class="h-4 w-4 mr-2" />
                                                Export
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                                <BookingTable
                                    :bookings="filteredBookings"
                                    @row-click="handleSelectBooking"
                                />
                            </div>
                        </div>
                    </template>
                    <template #fallback>
                        <div class="w-full h-96 flex justify-center items-center">
                            <div class="flex flex-col items-center">
                                <Loader2 class="animate-spin h-8 w-8 mb-2" />
                                <p class="text-gray-500">Loading booking analytics...</p>
                            </div>
                        </div>
                    </template>
                </Suspense>
            </div>

            <!-- Booking Details Sheet -->
            <Sheet :open="isSheetOpen" @update:open="handleSheetOpenChange">
                <SheetContent
                    :class="[
                        'h-full bg-white overflow-y-auto',
                        isMobile ? 'fixed inset-x-0 bottom-0 mt-24 rounded-t-[10px]' : 'sm:max-w-[800px] rounded-lg'
                    ]"
                    :side="isMobile ? 'bottom' : 'right'"
                >
                    <SheetHeader>
                        <SheetTitle>Booking Details</SheetTitle>
                        <SheetDescription>
                            View detailed information about this booking
                        </SheetDescription>
                    </SheetHeader>
                    <div class="px-4 py-6">
                        <BookingInfo
                            v-if="selectedBooking"
                            :booking="selectedBooking"
                            @close="isSheetOpen = false"
                        />
                    </div>
                </SheetContent>
            </Sheet>

            <!-- Insights Sheet -->
            <Sheet :open="showInsights" @update:open="handleInsightsSheetChange">
                <SheetContent
                    :class="[
                        'h-full bg-white overflow-y-auto',
                        isMobile ? 'fixed inset-x-0 bottom-0 mt-24 rounded-t-[10px]' : 'sm:max-w-[900px] rounded-lg'
                    ]"
                    :side="isMobile ? 'bottom' : 'right'"
                >
                    <SheetHeader>
                        <SheetTitle>Booking Insights & Analytics</SheetTitle>
                        <SheetDescription>
                            Real-time booking analytics, alerts, and quick actions
                        </SheetDescription>
                    </SheetHeader>
                    <div class="p-0">
                        <BookingInsightsSidebar
                            :bookings="bookingsData ?? []"
                            @close="showInsights = false"
                            :is-sheet="true"
                        />
                    </div>
                </SheetContent>
            </Sheet>
        </div>
        </BookingsTheme>
    </NuxtLayout>
</template>

<script setup lang="ts">
import type { Row } from '@tanstack/vue-table'
import type { IBooking } from '~/stores/booking/model/booking.model';
import { useBookingStore } from '~/stores/booking/booking.store';
import { BarChart3, Plus, Download, Loader2, Calendar } from 'lucide-vue-next';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetTitle
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';

useHead({
    title: 'Bookings Management'
})

definePageMeta({
    middleware: "auth"
})

const bookingStore = useBookingStore();

// Get bookings data
const { data: bookingsData } = await useAsyncData<IBooking[]>(
    'bookings',
    () => bookingStore.getBookings(1, 1000) // Get more bookings for analytics
);

// Check if the device is mobile
const isMobile = ref(false)
onMounted(() => {
    isMobile.value = window.innerWidth < 768
    window.addEventListener('resize', () => {
        isMobile.value = window.innerWidth < 768
    })
})

// Insights sheet state
const showInsights = ref(false);
const toggleInsights = () => {
    showInsights.value = !showInsights.value;
};

// Handle insights sheet open/close
const handleInsightsSheetChange = (isOpen: boolean) => {
    showInsights.value = isOpen;
};

// Filters state
const currentFilters = ref<any>({});
const filteredBookings = computed(() => {
    if (!bookingsData.value) return [];

    let filtered = [...bookingsData.value];

    // Apply search filter
    if (currentFilters.value.search) {
        const searchTerm = currentFilters.value.search.toLowerCase();
        filtered = filtered.filter(booking =>
            booking.booking_reference.toLowerCase().includes(searchTerm) ||
            `${booking.rider.first_name} ${booking.rider.last_name}`.toLowerCase().includes(searchTerm)
        );
    }

    // Apply status filter
    if (currentFilters.value.status) {
        filtered = filtered.filter(booking =>
            booking.status.toLowerCase() === currentFilters.value.status.toLowerCase()
        );
    }

    // Apply date range filter
    if (currentFilters.value.dateRange) {
        const { start, end } = currentFilters.value.dateRange;
        filtered = filtered.filter(booking => {
            const bookingDate = new Date(booking.booking_schedule_date);
            return bookingDate >= start && bookingDate <= end;
        });
    }

    // Apply revenue range filter
    if (currentFilters.value.revenueRange) {
        const range = currentFilters.value.revenueRange;
        filtered = filtered.filter(booking => {
            const amount = booking.total_amount;
            switch (range) {
                case '0-25': return amount >= 0 && amount <= 25;
                case '25-50': return amount > 25 && amount <= 50;
                case '50-100': return amount > 50 && amount <= 100;
                case '100+': return amount > 100;
                default: return true;
            }
        });
    }

    // Apply custom amount range
    if (currentFilters.value.minAmount || currentFilters.value.maxAmount) {
        const min = parseFloat(currentFilters.value.minAmount) || 0;
        const max = parseFloat(currentFilters.value.maxAmount) || Infinity;
        filtered = filtered.filter(booking =>
            booking.total_amount >= min && booking.total_amount <= max
        );
    }

    return filtered;
});

// Handle filter changes
const handleFiltersChanged = (filters: any) => {
    currentFilters.value = filters;
};

// Sheet state
const isSheetOpen = ref(false)
const selectedBooking = ref<IBooking | null>(null)

// Handle sheet open/close
const handleSheetOpenChange = (isOpen: boolean) => {
    isSheetOpen.value = isOpen
    if (!isOpen) {
        // Clear selected booking when sheet is closed
        selectedBooking.value = null
    }
}

// Handle booking selection
const handleSelectBooking = (row: Row<IBooking>) => {
    selectedBooking.value = row.original
    isSheetOpen.value = true
}

</script>