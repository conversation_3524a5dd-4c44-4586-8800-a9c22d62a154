<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kwanso - Loading</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        :root {
            --primary: #F97316; /* Orange shade */
            --light: #F8FAFC;
            --gray: #64748B;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--light);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 2rem;
        }

        .loader {
            width: 60px;
            height: 60px;
            border: 4px solid var(--gray);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1.2s linear infinite;
            margin-bottom: 1.5rem;
        }

        .tagline {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
            opacity: 0;
            animation: fadeIn 0.6s ease-in forwards;
        }

        .subtitle {
            font-size: 1rem;
            color: var(--gray);
            max-width: 320px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(8px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .loading-container {
                padding: 1rem;
            }

            .tagline {
                font-size: 1.1rem;
            }

            .subtitle {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="loader"></div>
        <h1 class="tagline">Kwanso</h1>
        <p class="subtitle">Getting your dashboard ready...</p>
    </div>
</body>
</html>