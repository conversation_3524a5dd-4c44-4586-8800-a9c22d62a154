import { useUserStore } from "~/stores/auth/user/user.store"

export default defineNuxtRouteMiddleware(async (to, from) => {
  const userStore = useUserStore()

  // If no token, redirect to login immediately
  if (!userStore.token) {
    return await navigateTo({ path: '/auth/login', replace: true })
  }

  // If user is still loading, wait for it
  if (userStore.currentUserApiState === 'LOADING') {
    try {
      await userStore.getCurrentUser()
    } catch (error) {
      console.error('Auth middleware: Failed to get user', error)
      return await navigateTo({ path: '/auth/login', replace: true })
    }
  }

  // Check if there is no user and move to the sign in page
  if (!userStore.successCurrentUser) {
    return await navigateTo({ path: '/auth/login', replace: true })
  }

  // // Navigate to new org page if there are no orgs
  // if (userStore.organisations.length == 0) {
  //   return await navigateTo({ path: '/org/new', replace: true })
  // }
})
