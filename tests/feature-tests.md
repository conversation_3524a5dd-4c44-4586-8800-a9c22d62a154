## Feature A1 — Auth-aware fetch with auto-refresh

Test steps:
1. Log in via `/auth/login` with a valid user to set `KWA_UT` and `KWA_RT` cookies.
2. Visit `/dashboard` (middleware `auth` should allow).
3. In the network tab, trigger an authenticated call (e.g., Shuttles page loads `/shuttles`). Verify `Authorization: Bearer <token>` header is present.
4. Expire the access token server-side (or shorten TTL), then navigate again to a protected page or trigger a fetch.
   - Expected: a transparent 401 → refresh flow → request retries once and succeeds. No redirect to login.
5. Manually clear cookies `KWA_UT` and `KWA_RT` and reload.
   - Expected: redirected to `/auth/login` (middleware).

Failure cases to validate:
- Refresh endpoint returns 401/400/500: user tokens cleared and next navigation redirects to login.

## Feature B1 — REST-based KPI metrics with fallback

Today Bookings and Available Drivers are fetched from REST endpoints with fallback to Parse functions.

Test steps:
1. With backend providing REST endpoints:
   - `GET /metrics/today-bookings` → `{ result: { count, total } }`
   - `GET /metrics/available-drivers` → `{ result: { count, total } }`
   Load `/dashboard` and verify values match the REST responses.
2. Simulate REST endpoints unavailable (return 404/500) while Parse functions are available:
   - Expected: values load via fallback without visible error.
3. Simulate both REST and fallback failing:
   - Expected: KPIs render `0` with no uncaught errors; console may show handled error.

Edge cases:
- Slow endpoints: loaders visible until data arrives (check Lucide loader icons in stats cards).
- Auth expired: A1 wrapper should refresh and metrics should still load.


