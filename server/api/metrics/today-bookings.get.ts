export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()

  try {
    // Prefer a first-party endpoint if available
    const apiBase = config.public.API_BASE_URL
    if (!apiBase) {
      throw createError({ statusCode: 500, statusMessage: 'Missing API base URL' })
    }

    // Try REST metrics first (backend may already expose it)
    try {
      const rest = await $fetch<{ result: { count: number; total: number } }>(
        `${apiBase}/metrics/today-bookings`,
        { method: 'GET' }
      )
      return rest
    } catch (_) {
      // Fallback to Parse cloud function if REST not available
      const parseHeaders: Record<string, string> = {
        'X-Parse-Application-Id': `${config.B4A_APP_ID}`,
        'X-Parse-REST-API-Key': `${config.B4A_API_KEY}`,
        'X-Parse-Master-Key': `${config.B4A_MASTER_KEY}`,
        'Content-Type': 'application/json',
      }

      const data = await $fetch<any>(`${apiBase}/functions/getTodayBookingsCount`, {
        method: 'POST',
        headers: parseHeaders,
      })

      if (data?.result && typeof data.result.count === 'number') {
        return { result: { count: data.result.count, total: data.result.total ?? 0 } }
      }
      return { result: { count: 0, total: 0 } }
    }
  } catch (error: any) {
    throw createError({
      statusCode: error?.status || 500,
      statusMessage: error?.message || 'Failed to fetch today bookings metric',
    })
  }
})


