
import { useUserStore } from "~/stores/auth/user/user.store";

export default defineNuxtPlugin(async () => {
    const userStore = useUserStore();

    // Only run on client-side to avoid SSR issues
    if (process.client) {
        // Check if we have a token before trying to get the user
        if (userStore.token) {
            try {
                // If the current user does not exist or is not successfully loaded, get it
                if (!userStore.successCurrentUser && userStore.currentUserApiState !== 'LOADING') {
                    console.log("User not loaded, getting user");
                    await userStore.getCurrentUser();
                }
            } catch (error) {
                console.error("Error fetching user in auth plugin:", error);
                // If there's an error getting the user, clear the tokens
                // This will force the user to log in again
                userStore.clearUserToken();
            }
        }
    }
})
