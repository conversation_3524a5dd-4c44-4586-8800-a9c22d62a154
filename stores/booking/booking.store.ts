import { ApiResponseState } from "~/utils/enum/apiResponseState.enum";
import { BookingModel, type IBooking } from "./model/booking.model";
import { useUserStore } from "../auth/user/user.store";
import type { IBookingStats } from "./interface/booking.interface";


export const useBookingStore = defineStore('booking', {
    state: () => ({
        // BOOKINGS LIST
        apiResponseState: ApiResponseState.NULL,
        failure: { message: "" },
        bookings: [] as IBooking[],
        selectedBooking: null as IBooking | null,

        // PAGINATION
        page: 1,
        limit: 10,
        totalCount: 0,

        booking_stats: {
            data: {} as IBookingStats,
            error: null as Error | null,
            loading: false
        },
    }),

    actions: {
         async getBookingStats(): Promise<IBookingStats> {
            try {
                this.booking_stats.loading = true;
                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/bookings/stats`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("BOOKING STATS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch booking stats");
                }

                this.booking_stats.data = (data.value as IBookingStats);

                return this.booking_stats.data;
            }
            catch (err) {
                console.error("ORG STATS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
            finally {
                this.booking_stats.loading = false;
            }
        },

        async getBookings(page: number = 1, limit: number = 10): Promise<IBooking[]> {
            return new Promise<IBooking[]>(async (resolve, reject) => {
                try {
                    this.apiResponseState = ApiResponseState.LOADING;

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/bookings`, {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                        params: {
                            page,
                            limit
                        }
                    });

                    // Handle errors
                    if (error.value) {
                        this.apiResponseState = ApiResponseState.FAILED;
                        this.failure.message = error.value?.data?.error || 'Server error! Could not fetch bookings';
                        return reject(this.failure.message);
                    }

                    // Success
                    const responseData = data.value as any;
                    
                    // Check if the response is an array or has a data property
                    const bookingsData = Array.isArray(responseData) ? responseData : 
                                        (responseData.data || []);
                    
                    // Update pagination info if available
                    if (responseData.page) this.page = responseData.page;
                    if (responseData.limit) this.limit = responseData.limit;
                    if (responseData.count) this.totalCount = responseData.count;
                    
                    // Transform the data using the BookingModel
                    const bookings = bookingsData.map((booking: any) => BookingModel.fromMap(booking));
                    
                    // Update state
                    this.bookings = bookings;
                    this.apiResponseState = ApiResponseState.SUCCESS;
                    
                    resolve(bookings);
                } catch (error) {
                    this.apiResponseState = ApiResponseState.FAILED;
                    this.failure.message = 'Server error! Could not fetch bookings';
                    reject(this.failure.message);
                }
            });
        },

        selectBooking(bookingId: string) {
            this.selectedBooking = this.bookings.find(booking => booking.id === bookingId) || null;
        },

        exportToCSV() {
            return useExportToCSV({
                fileName: `Kwanso_Bookings_${Date.now()}`,
                header: ["ID", "Reference", "Rider", "Route", "Status", "Amount", "Tickets", "Departure"],
                records: this.transformToCSVData()
            });
        },

        transformToCSVData(): any[] {
            const csvData: any[] = [];

            // Transform each booking object into the desired format
            this.bookings.forEach(booking => {
                const {
                    id,
                    booking_reference,
                    rider,
                    route,
                    status,
                    total_amount,
                    number_of_tickets,
                    departure_time
                } = booking;

                // Format the departure time
                const departureDate = useFormatDateHuman(new Date(departure_time));
                const departureTime = useFormatTimeHuman(new Date(departure_time));

                // Rider full name
                const riderName = `${rider.first_name} ${rider.last_name}`;

                // Route description
                const routeDescription = `${route.origin.name} - ${route.destination.name}`;

                // Push the transformed values into the CSV data array
                csvData.push([
                    id, 
                    booking_reference, 
                    riderName, 
                    routeDescription, 
                    status, 
                    total_amount, 
                    number_of_tickets, 
                    `${departureDate} ${departureTime}`
                ]);
            });

            return csvData;
        }
    },

    getters: {
        // BOOKINGS
        hasBookings: (state) => state.apiResponseState == ApiResponseState.SUCCESS && state.bookings.length > 0,
        isLoading: (state) => state.apiResponseState == ApiResponseState.LOADING,
        failed: (state) => state.apiResponseState == ApiResponseState.FAILED,
        success: (state) => state.apiResponseState == ApiResponseState.SUCCESS,
    }
});
