import { ApiResponseState } from "~/utils/enum/apiResponseState.enum";
import type { APIResponse } from "~/utils/types/response.type";
import { type IRoute, RouteModel, type IRouteShuttle } from "./model/route.model";
import type { AssignRouteDTO } from "./dto/assign.dto";
import type { NewRouteDTO } from "./dto/newRoute.dto";
import { ShuttleClassModel } from "../shuttle/model/shuttleClass.model";
import type { UpdateRouteShuttleDTO } from "../shuttle/dto/updateShuttleRoute.dto";
import { useUserStore } from "../auth/user/user.store";
import type { IRouteStats } from "./interface/route.interface";


export const useRouteStore = defineStore('Route', {
    state: () => ({
        apiResponseState: ApiResponseState.NULL,
        failure: { message: "" },
        routes: [] as IRoute[],
        selectedRoute: {} as IRoute,


        // SHUTTLE ACTIVE ROUTE
        activeShuttleRoute: {} as IRoute,
        activeShuttleRouteResponseState: ApiResponseState.NULL,
        failureActiveShuttleRoute: { message: "" },


        // ASSIGN ROUTE
        assignedRoutesToShuttle: [] as IRouteShuttle[],
        apiAssignRouteResponseState: ApiResponseState.NULL,
        failureAssignRoute: { message: "" },

        // DRIVER ROUTES
        driverRoutes: [] as IRouteShuttle[],
        driverRoutesResponseState: ApiResponseState.NULL,
        failureDriverRoutes: { message: "" },

        // NEW ROUTE
        newRoute: {} as IRoute,
        apiNewRouteState: ApiResponseState.NULL,
        failureNewRoute: { message: "" },

        // DELETE SHUTTLE ROUTE
        apiDeleteShuttleRouteResponseState: ApiResponseState.NULL,
        failureDeleteShuttleRoute: { message: "" },

        // ROUTE STATUS
        apiRouteStatusResponseState: ApiResponseState.NULL,
        selectedStatusId: "",
        routeStatusFailure: { message: "" },
        routeStatuses: [] as {
            objectId: string;
            name: string;
            createdAt: any;
        }[],

        // UPDATE ROUTE SHUTTLE
        apiUpdateShuttleRouteResponseState: ApiResponseState.NULL,
        failureUpdateShuttleRoute: { message: "" },

        // GET ACTIVE ROUTES COUNT
        apiActiveRoutesCountResponseState: ApiResponseState.NULL,
        activeRoutesCount: {
            count: 0,
            total: 0
        },
        failureActiveRoutesCount: { message: "" },

        //LATEST SCHEDULED SHUTTLE ROUTES
        apiGetLatestShuttleSchedulesResponseState: ApiResponseState.NULL,
        latestShuttleSchedules: [] as any[],
        failureGetLatestShuttleSchedules: { message: "" },

        // SHUTTLE ROUTES COUNT
        apiShuttleRoutesCountResponseState: ApiResponseState.NULL,
        shuttleRoutesCount: 0,
        failureShuttleRoutesCount: { message: "" },

        // SHUTTLE COMPLETED ROUTES COUNT
        apiShuttleCompletedRoutesCountResponseState: ApiResponseState.NULL,
        shuttleCompletedRoutesCount: 0,
        failureShuttleCompletedRoutesCount: { message: "" },

        stats: {
            data: {} as IRouteStats,
            error: null as Error | null,
            loading: false
        },
    }),
    actions: {
        async getRouteStats(): Promise<IRouteStats> {
            try {
                this.stats.loading = true;
                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/routes/stats`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG ROUTE STATS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch organization route stats");
                }

                return data.value as any;
            }
            catch (err) {
                console.error("ORG ROUTE STATS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
            finally {
                this.stats.loading = false;
            }
        },
        async getLocationPrediction(location: string | number): Promise<{
            description: string,
            placeId: string
        }[]> {
            return new Promise<any[]>(async (resolve, reject) => {
                try {
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/location/predictions`, {
                        method: 'GET',
                        params: {
                            location
                        }
                    });

                    // Handle errors
                    if (error.value) {
                        return reject(error.value?.data.error || 'Server error! Could not get predictions');
                    }

                    // Success
                    resolve(data.value as any);

                } catch (error) {

                    reject('Server error! Could not fetch Routes');
                }
            });
        },

        async getPlaceCoordinates(placeId: string): Promise<{ lat: number, long: number }> {
            return new Promise<{ lat: number, long: number }>(async (resolve, reject) => {
                try {
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/location/coordinates`, {
                        method: 'GET',
                        params: {
                            placeId
                        }
                    });

                    // Handle errors
                    if (error.value) {
                        return reject(error.value?.data.error || 'Server error! Could not get coordinates');
                    }

                    // Success
                    resolve(data.value as { lat: number, long: number });

                } catch (error) {
                    reject('Server error! Could not fetch coordinates');
                }
            });
        },

        async createNewRoute(route: NewRouteDTO): Promise<IRoute> {
            return new Promise<IRoute>(async (resolve, reject) => {
                try {
                    this.apiNewRouteState = ApiResponseState.LOADING;

                    const { data, error } = await useFetch<IRoute>(`${useRuntimeConfig().public.API_BASE_URL}/route`, {
                        method: 'POST',
                        body: route,
                    });

                    if (error.value) {
                        throw new Error(error.value.message || 'Failed to create new route');
                    }

                    this.apiNewRouteState = ApiResponseState.SUCCESS;
                    resolve(data.value as IRoute);

                } catch (error) {
                    console.error('Error creating new route:', error);
                    this.apiNewRouteState = ApiResponseState.FAILED;
                    reject('Internal Server error! Could not create new route');
                }
            });
        },


        async updateRouteShuttle(routeShuttle: UpdateRouteShuttleDTO) {
            try {
                this.apiUpdateShuttleRouteResponseState = ApiResponseState.LOADING;

                // SERVER LOGIC
                const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/routeShuttle`, {
                    method: 'PUT',
                    body: routeShuttle,
                    headers: {
                        'Access-Control-Allow-Origin': '*'
                    }
                })

                const apiData = (data.value as any)

                // Handle failure
                if (error.value) {
                    this.apiUpdateShuttleRouteResponseState = ApiResponseState.FAILED;
                    this.failureUpdateShuttleRoute.message = error.value.data.data.error
                    return;
                }
                // end of SERVER LOGIC

                this.apiUpdateShuttleRouteResponseState = ApiResponseState.SUCCESS;

            } catch (error) {

                // Handle login error
                this.failureUpdateShuttleRoute.message = 'Server error! Could not update route shuttle';
                this.apiUpdateShuttleRouteResponseState = ApiResponseState.FAILED;
            }
        },

        async getRoutes(id?: string): Promise<IRoute[]> {
            return new Promise<IRoute[]>(async (resolve, reject) => {
                try {
                    this.apiResponseState = ApiResponseState.LOADING;

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/routes`, {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                        params: id ? { id } : undefined
                    });

                    // Handle errors
                    if (error.value) {
                        this.apiResponseState = ApiResponseState.FAILED;
                        this.failure.message = error.value?.data?.error || 'Server error! Could not fetch Routes';
                        return reject(this.failure.message);
                    }

                    // Success
                    const responseData = data.value as any;
                    const routesData = responseData.data || [];

                    // Transform the data using the RouteModel
                    const routes = routesData.map((route: any) => RouteModel.fromMap(route));

                    // Update state
                    this.routes = routes;
                    this.apiResponseState = ApiResponseState.SUCCESS;

                    resolve(routes);
                } catch (error) {
                    this.apiResponseState = ApiResponseState.FAILED;
                    this.failure.message = 'Server error! Could not fetch Routes';
                    reject(this.failure.message);
                }
            });
        },

        async getLatestShuttleSchedules(): Promise<any[]> {
            return new Promise<IRoute[]>(async (resolve, reject) => {
                try {
                    this.apiGetLatestShuttleSchedulesResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/schedule/latest`, {
                        method: 'GET',
                    })

                    // Handle failure
                    if (error.value) {
                        this.apiGetLatestShuttleSchedulesResponseState = ApiResponseState.FAILED;
                        this.failureGetLatestShuttleSchedules.message = error.value.data.data.error
                        return resolve([]);
                    }
                    // end of SERVER LOGIC

                    this.latestShuttleSchedules = data.value as any;
                    this.apiGetLatestShuttleSchedulesResponseState = ApiResponseState.SUCCESS;
                    return resolve(this.latestShuttleSchedules)

                } catch (error) {

                    // Handle login error
                    this.failureGetLatestShuttleSchedules.message = 'Server error! Could not get latest shuttle schedules';
                    this.apiGetLatestShuttleSchedulesResponseState = ApiResponseState.FAILED;
                    return resolve([])

                }
            })
        },

        async getShuttleActiveRoute(shuttleId: string) {
            return new Promise<IRoute>(async (resolve, reject) => {
                try {
                    this.activeShuttleRouteResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/active/by/shuttle`, {
                        method: 'GET',
                        params: {
                            shuttleId
                        }
                    })

                    const apiData = (data.value as APIResponse<any>)

                    console.log(apiData)

                    // Handle failure
                    if (error.value) {
                        this.activeShuttleRouteResponseState = ApiResponseState.FAILED;
                        this.failureAssignRoute.message = error.value.data.data.error
                        resolve(this.activeShuttleRoute = {} as IRoute)
                        return;
                    }

                    if (!apiData.success) {
                        this.apiAssignRouteResponseState = ApiResponseState.FAILED;
                        this.failureAssignRoute.message = apiData.error.error //This way because of Kwanso api
                        resolve(this.activeShuttleRoute = {} as IRoute)
                        return;
                    }
                    // end of SERVER LOGIC

                    this.activeShuttleRoute = apiData.data
                    this.apiAssignRouteResponseState = ApiResponseState.SUCCESS;
                    resolve(this.activeShuttleRoute)

                } catch (error) {
                    this.failureAssignRoute.message = 'Server error! Could not fetch route assigned to shuttle';
                    this.apiAssignRouteResponseState = ApiResponseState.FAILED;
                    resolve(this.activeShuttleRoute = {} as IRoute)
                }
            });

        },

        async getRoutesAssignedToShuttle(shuttleId: string) {
            return new Promise<IRouteShuttle[]>(async (resolve, reject) => {
                try {
                    this.apiAssignRouteResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/assign/to/shuttle`, {
                        method: 'GET',
                        params: {
                            shuttleId
                        }
                    })



                    // Handle failure
                    if (error.value) {
                        this.apiAssignRouteResponseState = ApiResponseState.FAILED;
                        this.failureAssignRoute.message = error.value.data.data.error
                        resolve(this.assignedRoutesToShuttle = [] as IRouteShuttle[])
                        return;
                    }
                    // end of SERVER LOGIC

                    this.assignedRoutesToShuttle = (data.value as IRouteShuttle[])
                    this.apiAssignRouteResponseState = ApiResponseState.SUCCESS;
                    resolve(this.assignedRoutesToShuttle)

                } catch (error) {
                    this.failureAssignRoute.message = 'Server error! Could not fetch route assigned to shuttle';
                    this.apiAssignRouteResponseState = ApiResponseState.FAILED;
                    resolve(this.assignedRoutesToShuttle = [] as IRouteShuttle[])
                }
            });

        },

        async getAllRoutesByDriver(driverId: string) {
            return new Promise<IRouteShuttle[]>(async (resolve, reject) => {
                try {
                    this.driverRoutesResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/assign/to/driver`, {
                        method: 'GET',
                        params: {
                            driverId
                        }
                    })

                    console.log(driverId)
                    console.log("DATA: ", data.value)
                    console.log("ERROR: ", error.value)


                    // Handle failure
                    if (error.value) {
                        this.driverRoutesResponseState = ApiResponseState.FAILED;
                        this.failureAssignRoute.message = error.value.data.data.error
                        resolve(this.driverRoutes = [] as IRouteShuttle[])
                        return;
                    }

                    this.driverRoutes = data.value as IRouteShuttle[]
                    this.driverRoutesResponseState = ApiResponseState.SUCCESS;
                    resolve(this.driverRoutes)

                } catch (error) {
                    console.log(error)
                    this.failureAssignRoute.message = 'Server error! Could not fetch route assigned to shuttle';
                    this.driverRoutesResponseState = ApiResponseState.FAILED;
                    resolve(this.assignedRoutesToShuttle = [] as IRouteShuttle[])
                }
            });

        },

        async assignRouteToShuttle(options: AssignRouteDTO) {
            try {
                this.apiAssignRouteResponseState = ApiResponseState.LOADING;

                // SERVER LOGIC
                const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/assign/to/shuttle`, {
                    method: 'POST',
                    body: options
                })

                const apiData = (data.value as APIResponse<any>)

                // Handle failure
                if (error.value) {
                    this.apiAssignRouteResponseState = ApiResponseState.FAILED;
                    this.failureAssignRoute.message = error.value.data.data.error
                    return;
                }

                if (!apiData.success) {
                    this.apiAssignRouteResponseState = ApiResponseState.FAILED;
                    this.failureAssignRoute.message = apiData.message ?? '' //This way because of Kwanso api
                    return;
                }
                // end of SERVER LOGIC

                this.apiAssignRouteResponseState = ApiResponseState.SUCCESS;

            } catch (error: any) {
                this.failureAssignRoute.message = 'Server error! Could not assign route to shuttle';
                this.apiAssignRouteResponseState = ApiResponseState.FAILED;
            }
        },

        selectRoute(routeId: string) {
            this.selectedRoute = this.routes.find(route => route.id === routeId || route.objectId === routeId) ?? RouteModel.default();
        },

        async deletShuttleRoute(routeId: string) {

            console.log("RECORD ID: ", routeId)

            try {
                this.apiDeleteShuttleRouteResponseState = ApiResponseState.LOADING;

                // SERVER LOGIC
                const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/shuttle`, {
                    method: 'DELETE',
                    query: { routeId }
                })

                const apiData = (data.value as APIResponse<any>)



                // Handle failure
                if (error.value) {

                    this.apiDeleteShuttleRouteResponseState = ApiResponseState.FAILED;
                    this.failureDeleteShuttleRoute.message = error.value.data.data.error
                    return;
                }

                if (!apiData.success) {
                    this.apiDeleteShuttleRouteResponseState = ApiResponseState.FAILED;
                    this.failureDeleteShuttleRoute.message = apiData.error.error //This way because of Kwanso api
                    return;
                }
                // end of SERVER LOGIC

                this.apiDeleteShuttleRouteResponseState = ApiResponseState.SUCCESS;


            } catch (error) {
                console.log(error)
                // Handle login error
                this.failureDeleteShuttleRoute.message = 'Server error! Could not delete shuttle';
                this.apiDeleteShuttleRouteResponseState = ApiResponseState.FAILED;
            }
        },

        async getRouteStatuses(id?: string) {
            try {
                this.apiRouteStatusResponseState = ApiResponseState.LOADING;

                // SERVER LOGIC
                const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/status`, {
                    method: 'GET',
                    params: {
                        id
                    }
                })

                const apiData = (data.value as APIResponse<ShuttleClassModel[]>)

                // Handle failure
                if (error.value) {
                    this.apiRouteStatusResponseState = ApiResponseState.FAILED;
                    this.routeStatusFailure.message = error.value.data.data.error
                    return;
                }

                if (!apiData.success) {
                    this.apiRouteStatusResponseState = ApiResponseState.FAILED;
                    this.routeStatusFailure.message = apiData.error.error //This way because of Kwanso api
                    return;
                }
                // end of SERVER LOGIC


                // Route statuses data
                this.routeStatuses = apiData.data!.map(shuttle => ShuttleClassModel.fromMap(shuttle).shuttleClass)
                this.apiRouteStatusResponseState = ApiResponseState.SUCCESS;

            } catch (error) {
                // Handle error
                this.routeStatusFailure.message = 'Server error! Could not fetch route statuses';
                this.apiRouteStatusResponseState = ApiResponseState.FAILED;
            }
        },

        async getActiveRoutesCount(): Promise<{ count: number, total: number }> {
            return new Promise<{ count: number, total: number }>(async (resolve, reject) => {
                try {

                    this.apiActiveRoutesCountResponseState = ApiResponseState.LOADING; //Loading

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/route/active/count`, {
                        method: 'GET',
                    });

                    // Handle errors
                    if (error.value) {
                        console.log("ACTIVE ROUTES COUNT ERROR: ", (data.value as any))
                        this.apiActiveRoutesCountResponseState = ApiResponseState.FAILED;
                        return reject(error.value?.data.error || 'Server error! Could not get active routes count');
                    }

                    // Success
                    this.activeRoutesCount = {
                        count: (data.value as any).result.count,
                        total: (data.value as any).result.total
                    }
                    this.apiActiveRoutesCountResponseState = ApiResponseState.SUCCESS;
                    resolve(this.activeRoutesCount);

                } catch (error) {
                    console.log(error)
                    this.apiActiveRoutesCountResponseState = ApiResponseState.FAILED;
                    reject('Internal Server error! Could not get active routes count');
                }
            });
        },

        async getShuttleRoutesCount(shuttleId: string, status?: string): Promise<number> {
            return new Promise<number>(async (resolve) => {
                try {
                    this.apiShuttleRoutesCountResponseState = ApiResponseState.LOADING;

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/routes/shuttle/${shuttleId}/count`, {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                        query: {
                            status
                        }
                    });

                    // Handle errors
                    if (error.value) {
                        console.error("SHUTTLE ROUTES COUNT ERROR:", error.value);
                        this.apiShuttleRoutesCountResponseState = ApiResponseState.FAILED;
                        this.failureShuttleRoutesCount.message = error.value.data?.error || 'Server error! Could not get shuttle routes count';
                        return resolve(0); // Return 0 as default on error
                    }

                    // Success
                    const count = typeof data.value === 'number'
                        ? data.value
                        : (data.value as any)?.count || 0;

                    this.shuttleRoutesCount = count;
                    this.apiShuttleRoutesCountResponseState = ApiResponseState.SUCCESS;
                    resolve(count);

                } catch (error) {
                    console.error("Error getting shuttle routes count:", error);
                    this.apiShuttleRoutesCountResponseState = ApiResponseState.FAILED;
                    this.failureShuttleRoutesCount.message = 'Internal Server error! Could not get shuttle routes count';
                    resolve(0); // Return 0 as default on error
                }
            });
        },

        async getShuttleCompletedRoutesCount(shuttleId: string): Promise<number> {
            return new Promise<number>(async (resolve) => {
                try {
                    this.apiShuttleCompletedRoutesCountResponseState = ApiResponseState.LOADING;

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/routes/shuttle/${shuttleId}/count`, {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                        query: {
                            status: 'completed'
                        }
                    });

                    // Handle errors
                    if (error.value) {
                        console.error("SHUTTLE COMPLETED ROUTES COUNT ERROR:", error.value);
                        this.apiShuttleCompletedRoutesCountResponseState = ApiResponseState.FAILED;
                        this.failureShuttleCompletedRoutesCount.message = error.value.data?.error || 'Server error! Could not get completed routes count';
                        return resolve(0); // Return 0 as default on error
                    }

                    // Success
                    const count = typeof data.value === 'number'
                        ? data.value
                        : (data.value as any)?.count || 0;

                    this.shuttleCompletedRoutesCount = count;
                    this.apiShuttleCompletedRoutesCountResponseState = ApiResponseState.SUCCESS;
                    resolve(count);

                } catch (error) {
                    console.error("Error getting completed routes count:", error);
                    this.apiShuttleCompletedRoutesCountResponseState = ApiResponseState.FAILED;
                    this.failureShuttleCompletedRoutesCount.message = 'Internal Server error! Could not get completed routes count';
                    resolve(0); // Return 0 as default on error
                }
            });
        },



    },

    getters: {
        hasRoutes: (state) => state.apiResponseState == ApiResponseState.SUCCESS && state.routes.length > 0,
        isLoading: (state) => state.apiResponseState == ApiResponseState.LOADING,
        failed: (state) => state.apiResponseState == ApiResponseState.FAILED,
        success: (state) => state.apiResponseState == ApiResponseState.SUCCESS,

        // ASSIGN ROUTE
        failedAssignRoute: (state) => state.apiAssignRouteResponseState == ApiResponseState.FAILED,
        successAssignRoute: (state) => state.apiAssignRouteResponseState == ApiResponseState.SUCCESS,
        isLoadingAssignRoute: (state) => state.apiAssignRouteResponseState == ApiResponseState.LOADING,

        // NEW ROUTE
        failedNewRoute: (state) => state.apiNewRouteState == ApiResponseState.FAILED,
        successNewRoute: (state) => state.apiNewRouteState == ApiResponseState.SUCCESS,
        isLoadingNewRoute: (state) => state.apiNewRouteState == ApiResponseState.LOADING,

        // UPDATE ROUTE SHUTTLE
        failedUpdateRouteShuttle: (state) => state.apiUpdateShuttleRouteResponseState == ApiResponseState.FAILED,
        successUpdateRouteShuttle: (state) => state.apiUpdateShuttleRouteResponseState == ApiResponseState.SUCCESS,
        isUpdatingRouteShuttle: (state) => state.apiUpdateShuttleRouteResponseState == ApiResponseState.LOADING,

        // DELETE SHUTTLE ROUTE
        failedDeleteShuttleRoute: (state) => state.apiDeleteShuttleRouteResponseState == ApiResponseState.FAILED,
        successDeleteShuttleRoute: (state) => state.apiDeleteShuttleRouteResponseState == ApiResponseState.SUCCESS,
        deletingShuttleRoute: (state) => state.apiDeleteShuttleRouteResponseState == ApiResponseState.LOADING,

        // ROUTE STATUSES
        hasRouteStatuses: (state) => state.apiRouteStatusResponseState == ApiResponseState.SUCCESS && state.routeStatuses.length > 0,
        isLoadingRouteStatuses: (state) => state.apiRouteStatusResponseState == ApiResponseState.LOADING,
        failedRouteStatuses: (state) => state.apiRouteStatusResponseState == ApiResponseState.FAILED,
        successRouteStatuses: (state) => state.apiRouteStatusResponseState == ApiResponseState.SUCCESS,


        // GET ACTIVE ROUTES COUNT
        failedActiveRoutesCount: (state) => state.apiActiveRoutesCountResponseState == ApiResponseState.FAILED,
        successActiveRoutesCount: (state) => state.apiActiveRoutesCountResponseState == ApiResponseState.SUCCESS,
        isLoadingActiveRoutesCount: (state) => state.apiActiveRoutesCountResponseState == ApiResponseState.LOADING,

        // LATEST SHUTTLE SCHEDULES
        failedGetLatestShuttleSchedules: (state) => state.apiGetLatestShuttleSchedulesResponseState == ApiResponseState.FAILED,
        successGetLatestShuttleSchedules: (state) => state.apiGetLatestShuttleSchedulesResponseState == ApiResponseState.SUCCESS,
        isLoadingLatestShuttleSchedules: (state) => state.apiGetLatestShuttleSchedulesResponseState == ApiResponseState.LOADING,

        // SHUTTLE ROUTES COUNT
        failedShuttleRoutesCount: (state) => state.apiShuttleRoutesCountResponseState == ApiResponseState.FAILED,
        successShuttleRoutesCount: (state) => state.apiShuttleRoutesCountResponseState == ApiResponseState.SUCCESS,
        isLoadingShuttleRoutesCount: (state) => state.apiShuttleRoutesCountResponseState == ApiResponseState.LOADING,

        // SHUTTLE COMPLETED ROUTES COUNT
        failedShuttleCompletedRoutesCount: (state) => state.apiShuttleCompletedRoutesCountResponseState == ApiResponseState.FAILED,
        successShuttleCompletedRoutesCount: (state) => state.apiShuttleCompletedRoutesCountResponseState == ApiResponseState.SUCCESS,
        isLoadingShuttleCompletedRoutesCount: (state) => state.apiShuttleCompletedRoutesCountResponseState == ApiResponseState.LOADING
    }


})