import { UserModel, type User } from "~/stores/auth/user/model/user.model";
import { PartnerModel, type IPartner } from "~/stores/partner/model/partner.model";


export class ShuttleModel {
    shuttle: IShuttle;

    constructor(shuttle: IShuttle) {
        this.shuttle = shuttle
    }

    static fromMap(json: any) {
        const shuttle: IShuttle = {
            id: json.id,
            partner_id: json.partner_id,
            name: json.name,
            make: json.make,
            model: json.model,
            plate_number: json.plate_number,
            capacity: json.capacity,
            amenities: json.amenities || {
                wifi: false,
                reclining_seats: false,
                air_conditioning: false
            },
            status: json.status,
            created_at: json.created_at,
            updated_at: json.updated_at
        };

        return shuttle;
    }

    static toMap(shuttle: IShuttle) {
        return {
            "partner_id": shuttle.partner_id,
            "name": shuttle.name,
            "make": shuttle.make,
            "model": shuttle.model,
            "plate_number": shuttle.plate_number,
            "capacity": shuttle.capacity,
            "amenities": shuttle.amenities,
            "status": "active"
        }
    }

    static toUpdateMap(shuttle: Partial<IShuttle>) {
        return {
            "name": shuttle.name,
            "make": shuttle.make,
            "model": shuttle.model,
            "plate_number": shuttle.plate_number,
            "capacity": shuttle.capacity,
            "amenities": shuttle.amenities,
            "status": shuttle.status
        }
    }
}

export interface IShuttle {
    id: string;
    partner_id: string;
    name: string;
    make: string;
    model: string;
    plate_number: string;
    capacity: number;
    amenities: {
        wifi: boolean;
        reclining_seats: boolean;
        air_conditioning: boolean;
        [key: string]: boolean;
    };
    status: string;
    created_at: string;
    updated_at: string;
}



