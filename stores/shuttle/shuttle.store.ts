import { ApiResponseState } from "~/utils/enum/apiResponseState.enum";
import { ShuttleModel, type IShuttle } from "./model/shuttle.model";
import { ShuttleClassModel, type IShuttleClass } from "./model/shuttleClass.model";
import type { APIResponse } from "~/utils/types/response.type";
import type { NewShuttleDTO } from "./dto/newShuttle.dto";
import type { UpdateShuttleDTO } from "./dto/updateShuttle.dto";
import type { UpdateRouteShuttleDTO } from "./dto/updateShuttleRoute.dto";
import { useUserStore } from "../auth/user/user.store";
import type { IShuttleStats } from "./interface/shuttle.interface";

export const useShuttleStore = defineStore('shuttle', {
    state: () => ({
        // SHUTTLES LIST
        apiResponseState: ApiResponseState.NULL,
        failure: { message: "" },
        shuttles: [] as IShuttle[],
        selectedShuttle: null as IShuttle | null,

        // SHUTTLE CLASS
        apiShuttleClassResponseState: ApiResponseState.NULL,
        shuttleClassesFailure: { message: "" },
        shuttleClasses: [] as IShuttleClass[],

        // SHUTTLE STATUS
        apiShuttleStatusResponseState: ApiResponseState.NULL,
        shuttleStatusFailure: { message: "" },
        shuttleStatuses: [] as {
            objectId: string;
            name: string;
            createdAt: any;
        }[],

        // PARTNER SHUTTLES
        partnerShuttlesResponseState: ApiResponseState.NULL,
        partnerShuttlesFailure: { message: "" },
        partnerShuttles: [] as IShuttle[],

        // CREATE SHUTTLE
        apiCreateShuttleResponseState: ApiResponseState.NULL,
        failureCreateShuttle: { message: "" },

        // UPDATE SHUTTLE
        apiUpdateShuttleResponseState: ApiResponseState.NULL,
        failureUpdateShuttle: { message: "" },

        // ASSIGN DRIVER TO SHUTTLE
        assignDriverResponseState: ApiResponseState.NULL,
        failureAssignDriver: { message: "" },

        // DELETE SHUTTLE
        apiDeleteShuttleResponseState: ApiResponseState.NULL,
        failureDeleteShuttle: { message: "" },

        // GET SHUTTLE ASSIGNED TO DRIVER
        shuttleAssignedToDriver: null as IShuttle | null,
        apiGetShuttleAssignedToDriverResponseState: ApiResponseState.NULL,
        failureGetShuttleAssignedToDriver: { message: '' },

        // GET ACTIVE SHUTTLES COUNT
        apiActiveShuttlesCountResponseState: ApiResponseState.NULL,
        activeShuttlesCount: {
            count: 0,
            total: 0
        },
        failureActiveShuttlesCount: { message: "" },

        stats: {
            data: {} as IShuttleStats,
            error: null as Error | null,
            loading: false
        },
    }),
    actions: {
        async getShuttleStats(): Promise<IShuttleStats> {
            try {
                this.stats.loading = true;
                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/shuttles/stats`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("SHUTTLE STATS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch shuttle stats");
                }

                this.stats.data = (data.value as IShuttleStats);

                return this.stats.data;
            }
            catch (err) {
                console.error("ORG SHUTTLE STATS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
            finally {
                this.stats.loading = false;
            }
        },

        async createNewShuttle(shuttle: NewShuttleDTO): Promise<IShuttle> {
            return new Promise<IShuttle>(async (resolve, reject) => {
                try {
                    this.apiCreateShuttleResponseState = ApiResponseState.LOADING; //Loading

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/shuttles`, {
                        method: 'POST',
                        body: shuttle
                    });

                    // Handle errors
                    if (error.value) {
                        console.log("NEW SHUTTLE ERROR: ", error.value);
                        this.apiCreateShuttleResponseState = ApiResponseState.FAILED;
                        return reject(error.value?.data?.error || 'Server error! Could not create new shuttle');
                    }

                    // Success
                    this.apiCreateShuttleResponseState = ApiResponseState.SUCCESS;

                    // Convert the response to an IShuttle object
                    const newShuttle = ShuttleModel.fromMap(data.value);
                    resolve(newShuttle);

                } catch (error) {
                    console.error("Error creating shuttle:", error);
                    this.apiCreateShuttleResponseState = ApiResponseState.FAILED;
                    reject('Internal Server error! Could not create new shuttle');
                }
            });
        },

        async getShuttles(id?: string): Promise<IShuttle[]> {
            return new Promise<IShuttle[]>(async (resolve, reject) => {
                try {
                    this.apiResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const params = id ? { id } : undefined;
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/shuttles`, {
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                        method: 'GET',
                        params
                    });

                    // Handle errors
                    if (error.value) {
                        this.apiResponseState = ApiResponseState.FAILED;
                        return reject(error.value.data?.error || 'Server error! Could not fetch shuttles');
                    }

                    // Shuttle data
                    const shuttles = (data.value as any[]).map((shuttle: any) => ShuttleModel.fromMap(shuttle));

                    // Update state
                    this.apiResponseState = ApiResponseState.SUCCESS;
                    this.shuttles = shuttles;

                    resolve(shuttles);

                } catch (error) {
                    // Handle error
                    console.log("SOMETHING IS WRONG: ", error);
                    this.apiResponseState = ApiResponseState.FAILED;
                    reject(error); // Reject the promise for caller to handle
                }
            });
        },

        async getShuttlesByPartner(partnerId: string): Promise<IShuttle[]> {
            return new Promise<IShuttle[]>(async (resolve, reject) => {
                try {
                    this.partnerShuttlesResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/shuttle/by/partner`, {
                        method: 'GET',
                        query: {
                            partnerId
                        }
                    });

                    // Handle errors
                    if (!error?.value && !(data.value as any).success) {
                        this.partnerShuttlesResponseState = ApiResponseState.FAILED;
                        this.partnerShuttles = []
                        return resolve([]); // Resolve with empty array on failure
                    } else if (error?.value) {
                        this.partnerShuttles = []
                        this.partnerShuttlesResponseState = ApiResponseState.FAILED;
                        return reject(error.value.data.error || 'Server error! Could not fetch shuttles');
                    }

                    // Shuttle data
                    const shuttles = (data.value as any).data.map((shuttle: any) => ShuttleModel.fromMap(shuttle));

                    // Update state
                    this.partnerShuttlesResponseState = ApiResponseState.SUCCESS;
                    this.partnerShuttles = shuttles;

                    resolve(shuttles);

                } catch (error) {
                    // Handle error
                    console.log("SOMETHING IS WRONG: ", error);
                    this.partnerShuttles = []
                    this.partnerShuttlesResponseState = ApiResponseState.FAILED;
                    reject(error); // Reject the promise for caller to handle
                }
            });
        },

        //Get shuttle partner


        async getShuttleClasses(id?: string) {
            try {
                this.apiShuttleClassResponseState = ApiResponseState.LOADING;

                // SERVER LOGIC
                const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/shuttle/class`, {
                    method: 'GET',
                    params: {
                        id
                    }
                })

                const apiData = (data.value as APIResponse<ShuttleClassModel[]>)

                // Handle failure
                if (error.value) {
                    this.apiShuttleClassResponseState = ApiResponseState.FAILED;
                    this.shuttleClassesFailure.message = error.value.data.data.error
                    return;
                }

                if (!apiData.success) {
                    this.apiShuttleClassResponseState = ApiResponseState.FAILED;
                    this.shuttleClassesFailure.message = apiData.error.error //This way because of Kwanso api
                    return;
                }
                // end of SERVER LOGIC


                // Shuttle classes data
                this.shuttleClasses = apiData.data!.map(shuttle => ShuttleClassModel.fromMap(shuttle).shuttleClass)
                this.apiShuttleClassResponseState = ApiResponseState.SUCCESS;

            } catch (error) {
                // Handle login error
                this.shuttleClassesFailure.message = 'Server error! Could not fetch shuttle classes';
                this.apiShuttleClassResponseState = ApiResponseState.FAILED;
            }
        },

        async getShuttleStatuses(id?: string) {
            try {
                this.apiShuttleStatusResponseState = ApiResponseState.LOADING;

                // SERVER LOGIC
                const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/shuttle/status`, {
                    method: 'GET',
                    params: {
                        id
                    }
                })

                const apiData = (data.value as APIResponse<ShuttleClassModel[]>)

                // Handle failure
                if (error.value) {
                    this.apiShuttleStatusResponseState = ApiResponseState.FAILED;
                    this.shuttleStatusFailure.message = error.value.data.data.error
                    return;
                }

                if (!apiData.success) {
                    this.apiShuttleStatusResponseState = ApiResponseState.FAILED;
                    this.shuttleStatusFailure.message = apiData.error.error //This way because of Kwanso api
                    return;
                }
                // end of SERVER LOGIC


                // Shuttle classes data
                this.shuttleStatuses = apiData.data!.map(shuttle => ShuttleClassModel.fromMap(shuttle).shuttleClass)
                this.apiShuttleStatusResponseState = ApiResponseState.SUCCESS;

            } catch (error) {
                // Handle login error
                this.shuttleStatusFailure.message = 'Server error! Could not fetch shuttle classes';
                this.apiShuttleStatusResponseState = ApiResponseState.FAILED;
            }
        },

        async assignDriverToShuttle(options: { shuttleId: string, driverId: string }) {
            try {
                this.assignDriverResponseState = ApiResponseState.LOADING;

                // SERVER LOGIC
                const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/shuttle/assign/driver`, {
                    method: 'POST',
                    body: options
                })

                console.log("DATA: ", data)
                console.log("ERROR: ", error)

                const apiData = (data.value as APIResponse<ShuttleModel[]>)

                // Handle failure
                if (error.value) {
                    this.assignDriverResponseState = ApiResponseState.FAILED;
                    this.failureAssignDriver.message = error.value.data.data.error
                    return;
                }

                if (!apiData.success) {
                    this.assignDriverResponseState = ApiResponseState.FAILED;
                    this.failureAssignDriver.message = apiData.error.error //This way because of Kwanso api
                    return;
                }
                // end of SERVER LOGIC

                this.assignDriverResponseState = ApiResponseState.SUCCESS;

            } catch (error) {
                console.log(error)

                // Handle login error
                this.failureAssignDriver.message = 'Server error! Could not assign driver to shuttle';
                this.assignDriverResponseState = ApiResponseState.FAILED;
            }
        },

        async updateShuttle(shuttle: UpdateShuttleDTO) {
            try {
                this.apiUpdateShuttleResponseState = ApiResponseState.LOADING;

                // SERVER LOGIC
                const { error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/shuttles/${shuttle.id}`, {
                    method: 'PUT',
                    body: {
                        name: shuttle.name,
                        make: shuttle.make,
                        model: shuttle.model,
                        plate_number: shuttle.plate_number,
                        capacity: shuttle.capacity,
                        amenities: shuttle.amenities,
                        status: shuttle.status
                    }
                });

                // Handle failure
                if (error.value) {
                    this.apiUpdateShuttleResponseState = ApiResponseState.FAILED;
                    this.failureUpdateShuttle.message = error.value.data?.error || 'Server error! Could not update shuttle';
                    return;
                }
                // end of SERVER LOGIC

                this.apiUpdateShuttleResponseState = ApiResponseState.SUCCESS;

            } catch (error) {
                console.error("Error updating shuttle:", error);
                // Handle error
                this.failureUpdateShuttle.message = 'Server error! Could not update shuttle';
                this.apiUpdateShuttleResponseState = ApiResponseState.FAILED;
            }
        },



        async deletShuttle(shuttleId: string) {
            try {
                this.apiDeleteShuttleResponseState = ApiResponseState.LOADING;

                // SERVER LOGIC
                const { error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/shuttles/${shuttleId}`, {
                    method: 'DELETE'
                });

                // Handle failure
                if (error.value) {
                    this.apiDeleteShuttleResponseState = ApiResponseState.FAILED;
                    this.failureDeleteShuttle.message = error.value.data?.error || 'Server error! Could not delete shuttle';
                    return;
                }

                // end of SERVER LOGIC
                this.apiDeleteShuttleResponseState = ApiResponseState.SUCCESS;

                // Remove the deleted shuttle from the shuttles array
                this.shuttles = this.shuttles.filter(shuttle => shuttle.id !== shuttleId);

            } catch (error) {
                console.error("Error deleting shuttle:", error);
                // Handle error
                this.failureDeleteShuttle.message = 'Server error! Could not delete shuttle';
                this.apiDeleteShuttleResponseState = ApiResponseState.FAILED;
            }
        },

        // GET SHUTTLE ASSIGNED TO DRIVER
        async getShuttleAssignedToDriver(driverId: string): Promise<IShuttle> {
            return new Promise<IShuttle>(async (resolve, reject) => {
                try {
                    this.apiGetShuttleAssignedToDriverResponseState =
                        ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(
                        `${useRuntimeConfig().public.API_BASE_URL}/shuttle/by/driver`,
                        {
                            method: 'GET',
                            query: {
                                driverId
                            }
                        }
                    );

                    const apiData = (data.value as APIResponse<IShuttle>);

                    // Handle failure
                    if (error.value) {
                        this.apiGetShuttleAssignedToDriverResponseState =
                            ApiResponseState.FAILED;
                        this.failureGetShuttleAssignedToDriver.message =
                            error.value.data.data.error;
                        return reject();
                    }

                    if (!apiData.success) {
                        this.apiGetShuttleAssignedToDriverResponseState =
                            ApiResponseState.FAILED;
                        this.failureGetShuttleAssignedToDriver.message =
                            apiData.error.error; //This way because of Kwanso api
                        return reject();
                    }

                    this.shuttleAssignedToDriver = apiData.data as IShuttle;
                    // end of SERVER LOGIC

                    this.apiGetShuttleAssignedToDriverResponseState =
                        ApiResponseState.SUCCESS;

                    resolve(this.shuttleAssignedToDriver)
                } catch (error) {
                    console.log(error);
                    // Handle login error
                    this.failureGetShuttleAssignedToDriver.message =
                        'Server error! Could not get shuttle assigned to driver';
                    this.apiGetShuttleAssignedToDriverResponseState =
                        ApiResponseState.FAILED;
                    reject();
                }
            });
        },

        async getActiveShuttlesCount(): Promise<{ count: number, total: number }> {
            return new Promise<{ count: number, total: number }>(async (resolve, reject) => {
                try {

                    this.apiActiveShuttlesCountResponseState = ApiResponseState.LOADING; //Loading

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/shuttle/active/count`, {
                        method: 'GET',
                    });

                    // Handle errors
                    if (error.value) {
                        console.log("ACTIVE SHUTTLES COUNT ERROR: ", (data.value as any))
                        this.apiActiveShuttlesCountResponseState = ApiResponseState.FAILED;
                        return reject(error.value?.data.error || 'Server error! Could not get active shuttles count');
                    }

                    // Success
                    this.activeShuttlesCount = {
                        count: (data.value as any).result.count,
                        total: (data.value as any).result.total
                    }
                    this.apiActiveShuttlesCountResponseState = ApiResponseState.SUCCESS;
                    resolve(this.activeShuttlesCount);

                } catch (error) {
                    console.log(error)
                    this.apiActiveShuttlesCountResponseState = ApiResponseState.FAILED;
                    reject('Internal Server error! Could not get active shuttles count');
                }
            });
        },


        selectShuttle(shuttleId: string) {
            this.selectedShuttle = this.shuttles.find(shuttle => shuttle.id === shuttleId) || null;
        },

        exportToCSV() {
            return useExportToCSV({
                fileName: `Kwanso_Shuttles_${Date.now()}`,
                header: ["Id", "Make", "Model", "Date Added"],
                records: this.transformToCSVData()
            })
        },

        transformToCSVData(): any[] {
            const csvData: any[] = [];

            // Transform each shuttle object into the desired format
            this.shuttles.forEach(shuttle => {
                const {
                    id,
                    make,
                    model,
                    created_at
                } = shuttle;

                // Format the "Join Date" column
                const joinDate = useFormatDateHuman(new Date(created_at))

                // Push the transformed values into the CSV data array
                csvData.push([id, make, model, joinDate]);
            });

            return csvData;
        }
    },

    getters: {
        // SHUTTLES
        hasShuttles: (state) => state.apiResponseState == ApiResponseState.SUCCESS && state.shuttles.length > 0,
        isLoading: (state) => state.apiResponseState == ApiResponseState.LOADING,
        failed: (state) => state.apiResponseState == ApiResponseState.FAILED,
        success: (state) => state.apiResponseState == ApiResponseState.SUCCESS,

        // SHUTTLE CLASSES
        hasShuttleClasses: (state) => state.apiShuttleClassResponseState == ApiResponseState.SUCCESS && state.shuttleClasses.length > 0,
        isLoadingShuttleClasses: (state) => state.apiShuttleClassResponseState == ApiResponseState.LOADING,
        failedShuttleClasses: (state) => state.apiShuttleClassResponseState == ApiResponseState.FAILED,
        successShuttleClass: (state) => state.apiShuttleClassResponseState == ApiResponseState.SUCCESS,

        // SHUTTLE STATUSES
        hasShuttleStatuses: (state) => state.apiShuttleStatusResponseState == ApiResponseState.SUCCESS && state.shuttleStatuses.length > 0,
        isLoadingShuttleStatuses: (state) => state.apiShuttleStatusResponseState == ApiResponseState.LOADING,
        failedShuttleStatuses: (state) => state.apiShuttleStatusResponseState == ApiResponseState.FAILED,
        successShuttleStatuses: (state) => state.apiShuttleStatusResponseState == ApiResponseState.SUCCESS,

        // CREATE SHUTTLE
        failedCreateShuttle: (state) => state.apiCreateShuttleResponseState == ApiResponseState.FAILED,
        successCreateShuttle: (state) => state.apiCreateShuttleResponseState == ApiResponseState.SUCCESS,
        isLoadingCreateShuttle: (state) => state.apiCreateShuttleResponseState == ApiResponseState.LOADING,

        // ASSIGN DRIVER TO SHUTTLE
        failedAssignDriver: (state) => state.assignDriverResponseState == ApiResponseState.FAILED,
        successAssignDriver: (state) => state.assignDriverResponseState == ApiResponseState.SUCCESS,
        isLoadingAssignDriver: (state) => state.assignDriverResponseState == ApiResponseState.LOADING,

        // UPDATE SHUTTLE
        failedUpdateShuttle: (state) => state.apiUpdateShuttleResponseState == ApiResponseState.FAILED,
        successUpdateShuttle: (state) => state.apiUpdateShuttleResponseState == ApiResponseState.SUCCESS,
        isLoadingUpdateShuttle: (state) => state.apiUpdateShuttleResponseState == ApiResponseState.LOADING,

        // DELETE SHUTTLE
        failedDeleteShuttle: (state) => state.apiDeleteShuttleResponseState == ApiResponseState.FAILED,
        successDeleteShuttle: (state) => state.apiDeleteShuttleResponseState == ApiResponseState.SUCCESS,
        isLoadingDeleteShuttle: (state) => state.apiDeleteShuttleResponseState == ApiResponseState.LOADING,

        // GET SHUTTLE ASSIGNED TO DRIVER
        failedGetShuttleAssignedToDriver: (state) =>
            state.apiGetShuttleAssignedToDriverResponseState ===
            ApiResponseState.FAILED,
        successGetShuttleAssignedToDriver: (state) =>
            state.apiGetShuttleAssignedToDriverResponseState ===
            ApiResponseState.SUCCESS,
        isLoadingGetShuttleAssignedToDriver: (state) =>
            state.apiGetShuttleAssignedToDriverResponseState ===
            ApiResponseState.LOADING,
    }


})