import { useUserStore } from "../auth/user/user.store";


export const useOrganizationStore = defineStore('organization', {
    state: () => ({

    }),
    actions: {
        async getOrgStats(): Promise<any> {
            try {
                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/stats`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG STATS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch organization stats");
                }

                return data.value as any;
            }
            catch (err) {
                console.error("ORG STATS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
        },
        async getEarningsTrend(): Promise<[]> {
            try {
                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/performance/earnings/trend`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG STATS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch earnings trend");
                }

                return data.value as any;
            }
            catch (err) {
                console.error("ORG STATS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
        },
         async getBookingsTrend(): Promise<[]> {
            try {
                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/performance/bookings/trend`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG STATS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch bookings trend");
                }

                return data.value as any;
            }
            catch (err) {
                console.error("ORG STATS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
        }

    },
});
