import { useUserStore } from "../auth/user/user.store";
import type { IBooking } from "../booking/model/booking.model";


interface Schedule {
    id: string
    departure_time: string
    status: string
    booked_seats?: number
    route?: {
        origin: string
        destination: string
        base_price: number
    }
    shuttle?: {
        name: string
        capacity: number
    }
}

interface Customer {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    avatar_url?: string;
    created_at: string;
}

interface PayoutSummary {
    total_payouts_this_month: number;
    growth_percentage: number;
    completed: {
        count: number;
        amount: number;
    };
    pending: {
        count: number;
        amount: number;
    };
    scheduled: {
        count: number;
        amount: number;
        next_payout_days: number;
    };
    average_payout: number;
    total_payouts_count: number;
}

export const useOrganizationStore = defineStore('organization', {
    state: () => ({
        trends: {
            earnings: [] as any[],
            bookings: [] as any[],
            error: null as Error | null,
            loading: false
        },
        stats: {
            data: null as any,
            error: null as Error | null,
            loading: false
        },
        trend_stats: {
            data: null as null | { total: number; average: number; peak: number; growth: number },
            error: null as Error | null,
            loading: false
        },
        recent_bookings: {
            data: [] as IBooking[],
            error: null as Error | null,
            loading: false
        },
        today_schedules: {
            data: [] as Schedule[],
            loading: false,
            error: null as Error | null,
        },
        recent_customers: {
            data: [] as Customer[],
            loading: false,
            error: null as Error | null,
        },
        payout_summary: {
            data: null as any,
            error: null as Error | null,
            loading: false
        },
        
    }),
    actions: {
        async getOrgStats(): Promise<any> {
            try {
                this.stats.loading = true;
                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/stats`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG STATS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch organization stats");
                }

                return data.value as any;
            }
            catch (err) {
                console.error("ORG STATS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
            finally {
                this.stats.loading = false;
            }
        },
        async getEarningsTrend(): Promise<[]> {
            try {
                this.trends.loading = true;

                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/performance/earnings/trend`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG STATS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch earnings trend");
                }

                // Update stats
                this.trend_stats.data = (data.value as any).stats;

                return this.trends.earnings = (data.value as any).trend;
            }
            catch (err) {
                console.error("ORG STATS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
            finally {
                this.trends.loading = false;
            }
        },
        async getBookingsTrend(): Promise<[]> {
            try {
                this.trends.loading = true;

                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/performance/bookings/trend`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG STATS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch bookings trend");
                }

                // Update stats
                this.trend_stats.data = (data.value as any).stats;

                return this.trends.bookings = (data.value as any).trend;
            }
            catch (err) {
                console.error("ORG STATS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
            finally {
                this.trends.loading = false;
            }
        },
        async getRecentBookings(): Promise<IBooking[]> {
            try {
                this.recent_bookings.loading = true;

                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/bookings/recent`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG RECENT BOOKINGS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch recent bookings");
                }

                // Update bookings
                this.recent_bookings.data = (data.value as any);

                return this.recent_bookings.data;
            }
            catch (err) {
                console.error("ORG RECENT BOOKINGS EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
            finally {
                this.recent_bookings.loading = false;
            }
        },
        async getTodaySchedules(): Promise<Schedule[]> {
            try {
                this.today_schedules.loading = true;

                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/schedules/today`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG TODAY SCHEDULES ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch today's schedules");
                }

                // Update schedules
                this.today_schedules.data = (data.value as any);

                return this.today_schedules.data;
            }
            catch (err) {
                console.error("ORG TODAY SCHEDULES EXCEPTION:", err);
                throw err; // rethrow so caller handles it
            }
            finally {
                this.today_schedules.loading = false;
            }
        },
        async getRecentCustomers(): Promise<Customer[]> {
            try {
                this.recent_customers.loading = true;

                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/customers/recent`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG RECENT CUSTOMERS ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch recent customers");
                }

                this.recent_customers.data = (data.value as any);
                return this.recent_customers.data;
            }
            catch (err) {
                console.error("ORG RECENT CUSTOMERS EXCEPTION:", err);
                throw err;
            }
            finally {
                this.recent_customers.loading = false;
            }
        },
        async getPayoutSummary(): Promise<PayoutSummary> {
            try {
                this.payout_summary.loading = true;

                const { data, error } = await useFetch(
                    `${useRuntimeConfig().public.API_BASE_URL}/organization/payouts/summary`,
                    {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                    }
                );

                if (error.value) {
                    console.error("ORG PAYOUT SUMMARY ERROR:", error.value);
                    throw new Error(error.value.message || "Failed to fetch payout summary");
                }

                this.payout_summary.data = (data.value as any);
                return this.payout_summary.data;
            }
            catch (err) {
                console.error("ORG PAYOUT SUMMARY EXCEPTION:", err);
                throw err;
            }
            finally {
                this.payout_summary.loading = false;
            }
        }


    },
});
