import { ApiResponseState } from "~/utils/enum/apiResponseState.enum";
import { type User, UserModel } from "../auth/user/model/user.model";
import { type IRide, RideModel } from "./model/ride.model";
import { useUserStore } from "../auth/user/user.store";
import type { APIResponse } from "~/utils/types/response.type";

export const useRiderStore = defineStore('rider', {
    state: () => ({
        apiResponseState: ApiResponseState.NULL,
        failure: { message: "" },
        riders: [] as User[],

        // SHUTTLE RIDES
        ridesByShuttle: [] as IRide[],
        apiRidesByShuttleResponseState: ApiResponseState.NULL,
        failureRidesByShuttle: { message: "" },


        // ALL TRIPS
        allTrips: [] as IRide[],
        allTripsResponseState: ApiResponseState.NULL,
        failureAllTrips: { message: "" },

        // GET COMPLETED RIDES COUNT
        apiCompletedRidesCountResponseState: ApiResponseState.NULL,
        completedRidesCount: {
            count : 0,
            total : 0
        },
        failureCompletedRidesCount: { message: "" },

        // TODAY BOOKINGS
        todayBookings: { count: 0, total: 0 },
        loadingTodayBookings: false,
        errorTodayBookings: null as Error | null,
    }),
    actions: {

        async getRiders(id?: string) {
            return new Promise<User[]>(async (resolve, reject) => {
                try {
                    this.apiResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/auth/riders`, {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                        params: id ? { id } : undefined,
                    });

                    // Handle errors
                    if (error.value) {
                        this.apiResponseState = ApiResponseState.FAILED;
                        this.failure.message = error.value?.data?.error || 'Server error! Could not fetch riders';
                        return resolve([]);
                    }

                    // Process rider data
                    const responseData = data.value as any;
                    const ridersData = Array.isArray(responseData) ? responseData :
                                      (responseData.data || []);

                    // Transform the data using the UserModel
                    this.riders = ridersData.map((rider: any) => UserModel.fromMap(rider));

                    // Update state
                    this.apiResponseState = ApiResponseState.SUCCESS;

                    resolve(this.riders);
                } catch (error) {
                    // Handle error
                    console.log("SOMETHING IS WRONG: ", error);
                    this.apiResponseState = ApiResponseState.FAILED;
                    this.failure.message = 'Server error! Could not fetch riders';
                    resolve([]);
                }
            });
        },

        async getAllRides() {
            return new Promise<IRide[]>(async (resolve, reject) => {
                try {
                    this.allTripsResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/ride`, {
                        method: 'GET'
                    })

                    const apiData = (data.value as APIResponse<IRide[]>)

                    // Handle failure
                    if (error.value) {
                        this.apiResponseState = ApiResponseState.FAILED;
                        this.failure.message = error.value.data.data.error
                        this.allTrips = []
                        return resolve(this.allTrips);
                    }

                    if (!apiData.success) {
                        this.apiResponseState = ApiResponseState.FAILED;
                        this.failure.message = apiData.error.error //This way because of Kwanso api
                        this.allTrips = []
                        return resolve(this.allTrips);
                    }
                    // end of SERVER LOGIC


                    this.allTrips = apiData.data!
                    this.allTripsResponseState = ApiResponseState.SUCCESS;
                    return resolve(this.allTrips)

                } catch (error) {
                    this.failureAllTrips.message = 'Server error! Could not fetch rides';
                    this.allTripsResponseState = ApiResponseState.FAILED;
                    return resolve(this.allTrips = [])
                }
            });

        },

        async getRidesByShuttle(shuttleId: string) {
            return new Promise<IRide[]>(async (resolve, reject) => {
                try {
                    this.apiRidesByShuttleResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/ride/by/shuttle`, {
                        method: 'GET',
                        params: {
                            shuttleId
                        }
                    })

                    const apiData = (data.value as APIResponse<IRide[]>)


                    // Handle failure
                    if (error.value) {
                        this.apiRidesByShuttleResponseState = ApiResponseState.FAILED;
                        this.failureRidesByShuttle.message = error.value.data.data.error
                        resolve([])
                        return;
                    }

                    if (!apiData.success) {
                        this.apiRidesByShuttleResponseState = ApiResponseState.FAILED;
                        this.failureRidesByShuttle.message = apiData.error.error //This way because of Kwanso api
                        resolve([])
                        return;
                    }
                    // end of SERVER LOGIC


                    // Shuttle data
                    this.ridesByShuttle = apiData.data!

                    this.apiRidesByShuttleResponseState = ApiResponseState.SUCCESS;

                    resolve(this.ridesByShuttle)

                } catch (error) {
                    // Handle login error
                    console.log(error)
                    this.failureRidesByShuttle.message = 'Server error! Could not fetch rides by shuttle';
                    this.apiRidesByShuttleResponseState = ApiResponseState.FAILED;
                    resolve([])
                }
            }
            )
        },

        async getRidesByPartner(partnerId: string) {
            return new Promise<IRide[]>(async (resolve, reject) => {
                try {
                    this.apiRidesByShuttleResponseState = ApiResponseState.LOADING;

                    // SERVER LOGIC
                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/ride/by/partner`, {
                        method: 'GET',
                        params: {
                            partnerId
                        }
                    })

                    const apiData = (data.value as APIResponse<IRide[]>)


                    // Handle failure
                    if (error.value) {
                        this.apiRidesByShuttleResponseState = ApiResponseState.FAILED;
                        this.failureRidesByShuttle.message = error.value.data.data.error
                        resolve([])
                        return;
                    }

                    if (!apiData.success) {
                        this.apiRidesByShuttleResponseState = ApiResponseState.FAILED;
                        this.failureRidesByShuttle.message = apiData.error.error //This way because of Kwanso api
                        resolve([])
                        return;
                    }
                    // end of SERVER LOGIC


                    // Shuttle data
                    this.ridesByShuttle = apiData.data!

                    this.apiRidesByShuttleResponseState = ApiResponseState.SUCCESS;

                    resolve(this.ridesByShuttle)

                } catch (error) {
                    // Handle login error
                    console.log(error)
                    this.failureRidesByShuttle.message = 'Server error! Could not fetch rides by shuttle';
                    this.apiRidesByShuttleResponseState = ApiResponseState.FAILED;
                    resolve([])
                }
            }
            )
        },

        async getCompletedRidesCount(): Promise<{count:number, total:number}> {
            return new Promise<{count:number, total:number}>(async (resolve, reject) => {
                try {

                    this.apiCompletedRidesCountResponseState = ApiResponseState.LOADING; //Loading

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/ride/completed/count`, {
                        method: 'GET',
                    });

                    // Handle errors
                    if (error.value) {
                        console.log("COMPLETED RIDES COUNT ERROR: ", (data.value as any))
                        this.apiCompletedRidesCountResponseState = ApiResponseState.FAILED;
                        return reject(error.value?.data.error || 'Server error! Could not get completed rides count');
                    }

                    // Success
                    this.completedRidesCount = {
                        count : (data.value as any).result.count,
                        total : (data.value as any).result.total
                    }
                    this.apiCompletedRidesCountResponseState = ApiResponseState.SUCCESS;
                    resolve(this.completedRidesCount);

                } catch (error) {
                    console.log(error)
                    this.apiCompletedRidesCountResponseState = ApiResponseState.FAILED;
                    reject('Internal Server error! Could not get completed rides count');
                }
            });
        },

        async getTodayBookingsCount() {
            try {
                this.loadingTodayBookings = true;
                // Prefer REST metrics endpoint; fallback to Parse function if unavailable
                try {
                    const rest = await useAuthFetch<{ result: { count: number, total: number } }>(
                        '/api/metrics/today-bookings',
                        { method: 'GET' }
                    )
                    if (rest?.result) {
                        this.todayBookings = rest.result
                        return this.todayBookings
                    }
                } catch (restErr: any) {
                    // If REST metrics is missing (404) or fails, fallback to Parse function
                    const { data } = await useApiFetch('functions/getTodayBookingsCount', {
                        method: 'POST'
                    })
                    if (data?.result) {
                        this.todayBookings = data.result
                        return this.todayBookings
                    }
                    throw restErr
                }
                return { count: 0, total: 0 }
            } catch (error: any) {
                this.errorTodayBookings = error?.response?.data?.error || 'Failed to fetch today\'s bookings';
                return { count: 0, total: 0 };
            } finally {
                this.loadingTodayBookings = false;
            }
        },
    },

    getters: {
        hasRiders: (state) => state.apiResponseState == ApiResponseState.SUCCESS && state.riders.length > 0,
        isLoading: (state) => state.apiResponseState == ApiResponseState.LOADING,
        failed: (state) => state.apiResponseState == ApiResponseState.FAILED,
        success: (state) => state.apiResponseState == ApiResponseState.SUCCESS,


        // SHUTTLE RIDES
        hasShuttleRides: (state) => state.apiRidesByShuttleResponseState == ApiResponseState.SUCCESS && state.ridesByShuttle.length > 0,
        isLoadingShuttleRides: (state) => state.apiRidesByShuttleResponseState == ApiResponseState.LOADING,
        failedShuttleRides: (state) => state.apiRidesByShuttleResponseState == ApiResponseState.FAILED,
        successShuttleRides: (state) => state.apiRidesByShuttleResponseState == ApiResponseState.SUCCESS,

        // GET COMPLETED RIDES COUNT
        failedCompletedRidesCount: (state) => state.apiCompletedRidesCountResponseState == ApiResponseState.FAILED,
        successCompletedRidesCount: (state) => state.apiCompletedRidesCountResponseState == ApiResponseState.SUCCESS,
        isLoadingCompletedRidesCount: (state) => state.apiCompletedRidesCountResponseState == ApiResponseState.LOADING,

        // TODAY BOOKINGS
        isLoadingTodayBookings: (state) => state.loadingTodayBookings,
        errorTodayBookings: (state) => state.errorTodayBookings,
    }
})