import { ApiResponseState } from "~/utils/enum/apiResponseState.enum";
import { UserModel, type User } from "../auth/user/model/user.model";
import type { UpdateDriverDTO } from "./dto/updatedDriver.dto";
import { useUserStore } from "../auth/user/user.store";

export const useDriverStore = defineStore('driver', {
  state: () => ({
    apiResponseState: ApiResponseState.NULL,
    failure: { message: "" },
    drivers: [] as User[],
    selectedDriver: {} as User,

    driverStatuses: [] as any,

    availableDrivers: { count: 0, total: 0 },
    loading: false,
    error: null as Error | null,

    // UPDATE Driver
    apiUpdateDriverResponseState: ApiResponseState.NULL,
    failureUpdateDriver: { message: "" },

    // DELETE DRIVER
    apiDeleteDriverResponseState: ApiResponseState.NULL,
    failureDeleteDriver: { message: '' },

  }),
  actions: {

    async getDrivers(id?: string): Promise<User[]> {
      return new Promise<User[]>(async (resolve, reject) => {
        try {
          this.apiResponseState = ApiResponseState.LOADING;

          // SERVER LOGIC
          const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/auth/drivers`, {
            method: 'GET',
            headers: {
              "Authorization": `Bearer ${useUserStore().token}`
            },
            params: id ? { id } : undefined,
          });

          // Handle errors
          if (error.value) {
            this.apiResponseState = ApiResponseState.FAILED;
            this.failure.message = error.value?.data?.error || 'Server error! Could not fetch drivers';
            return reject(this.failure.message);
          }

          // Process driver data
          const responseData = data.value as any;
          const driversData = Array.isArray(responseData) ? responseData :
                             (responseData.data || []);

          // Transform the data using the UserModel
          this.drivers = driversData.map((driver: any) => UserModel.fromMap(driver));

          // Update state
          this.apiResponseState = ApiResponseState.SUCCESS;

          resolve(this.drivers);
        } catch (error) {
          // Handle error
          console.log("SOMETHING IS WRONG: ", error);
          this.apiResponseState = ApiResponseState.FAILED;
          this.failure.message = 'Server error! Could not fetch drivers';
          reject(this.failure.message);
        }
      });
    },

    async updateDriver(driver: UpdateDriverDTO) {
      try {
        this.apiUpdateDriverResponseState = ApiResponseState.LOADING;

        // SERVER LOGIC
        const { error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/driver`, {
          method: 'PUT',
          headers: {
            "Authorization": `Bearer ${useUserStore().token}`
          },
          body: driver
        })

        // Handle failure
        if (error.value) {
          this.apiUpdateDriverResponseState = ApiResponseState.FAILED;
          this.failureUpdateDriver.message = error.value.data?.error || 'Server error! Could not update driver';
          return;
        }
        // end of SERVER LOGIC

        this.apiUpdateDriverResponseState = ApiResponseState.SUCCESS;

      } catch (error) {
        // Handle login error
        this.failureUpdateDriver.message = 'Server error! Could not update driver';
        this.apiUpdateDriverResponseState = ApiResponseState.FAILED;
      }
    },
    // DELETE DRIVER
    async deleteDriver(driverId: string) {
      try {
        this.apiDeleteDriverResponseState = ApiResponseState.LOADING;

        // SERVER LOGIC
        const { error } = await useFetch(
          `${useRuntimeConfig().public.API_BASE_URL}/auth/user`,
          {
            method: 'DELETE',
            headers: {
              "Authorization": `Bearer ${useUserStore().token}`
            },
            query: { userId: driverId },
          }
        );

        // Handle failure
        if (error.value) {
          this.apiDeleteDriverResponseState = ApiResponseState.FAILED;
          this.failureDeleteDriver.message = error.value.data?.error || 'Server error! Could not delete driver';
          return;
        }
        // end of SERVER LOGIC

        this.apiDeleteDriverResponseState = ApiResponseState.SUCCESS;
      } catch (error) {
        console.log(error);
        // Handle login error
        this.failureDeleteDriver.message = 'Server error! Could not delete driver';
        this.apiDeleteDriverResponseState = ApiResponseState.FAILED;
      }
    },

    selectDriver(driverId: string) {
      this.selectedDriver = this.drivers.find(driver => driver.id === driverId) || {} as User;
    },

    async getAvailableDriversCount() {
      try {
        this.loading = true;
        // Prefer REST metrics endpoint; fallback to Parse function if unavailable
        try {
          const rest = await useAuthFetch<{ result: { count: number, total: number } }>(
            '/api/metrics/available-drivers',
            { method: 'GET' }
          )
          if (rest?.result) {
            this.availableDrivers = rest.result
            return this.availableDrivers
          }
        } catch (restErr: any) {
          const { data } = await useApiFetch('functions/getAvailableDriversCount', {
            method: 'POST'
          })
          if (data?.result) {
            this.availableDrivers = data.result
            return this.availableDrivers
          }
          throw restErr
        }
        return { count: 0, total: 0 }
      } catch (error: any) {
        this.error = error?.response?.data?.error || 'Failed to fetch available drivers';
        return { count: 0, total: 0 };
      } finally {
        this.loading = false;
      }
    },
  },

  getters: {
    hasDrivers: (state) => state.apiResponseState == ApiResponseState.SUCCESS && state.drivers.length > 0,
    isLoading: (state) => state.apiResponseState == ApiResponseState.LOADING,
    failed: (state) => state.apiResponseState == ApiResponseState.FAILED,
    success: (state) => state.apiResponseState == ApiResponseState.SUCCESS,

    // UPDATE DRIVER
    failedDriverUpdate: (state) => state.apiUpdateDriverResponseState == ApiResponseState.FAILED,
    successDriverUpdate: (state) => state.apiUpdateDriverResponseState == ApiResponseState.SUCCESS,
    isUpdatingDriver: (state) => state.apiUpdateDriverResponseState == ApiResponseState.LOADING,

    // DELETE DRIVER
    failedDeleteDriver: (state) =>
      state.apiDeleteDriverResponseState === ApiResponseState.FAILED,
    successDeleteDriver: (state) =>
      state.apiDeleteDriverResponseState === ApiResponseState.SUCCESS,
    isDeletingDriver: (state) =>
      state.apiDeleteDriverResponseState === ApiResponseState.LOADING,

  }
})