import { ApiResponseState } from "~/utils/enum/apiResponseState.enum";
import { useUserStore } from "../auth/user/user.store";

export interface TrendDataPoint {
    date: string;
    count: number;
}

export interface TrendPeriod {
    start: string;
    end: string;
}

export interface TrendFilters {
    interval: string;
    [key: string]: any;
}

export interface TrendData {
    trend: TrendDataPoint[];
    total: number;
    period: TrendPeriod;
    filters: TrendFilters;
}

export interface ISchedule {
    id: string;
    route_id: {
        id: string;
        name: string;
        origin: number;
        status: string;
        base_price: number;
        created_at: string;
        updated_at: string;
        destination: number;
    };
    shuttle_id: {
        id: string;
        make: string;
        name: string;
        model: string;
        status: string;
        capacity: number;
        amenities: {
            wifi: boolean;
            reclining_seats: boolean;
            air_conditioning: boolean;
            [key: string]: boolean;
        };
        created_at: string;
        partner_id: string;
        updated_at: string;
        plate_number: string;
    };
    departure_time: string;
    arrival_time: string;
    available_seats: number;
    price: number;
    status: string;
    created_at: string;
    updated_at: string;
    driver_id: string | null;
}

export const useScheduleStore = defineStore('schedule', {
    state: () => ({
        // COMPLETED TRIPS TREND
        apiCompletedTripsTrendResponseState: ApiResponseState.NULL,
        completedTripsTrend: null as TrendData | null,
        failureCompletedTripsTrend: { message: "" },

        // SCHEDULES BY SHUTTLE
        apiSchedulesByShuttleResponseState: ApiResponseState.NULL,
        schedulesByShuttle: [] as ISchedule[],
        failureSchedulesByShuttle: { message: "" },

        // SCHEDULES BY DRIVER
        apiSchedulesByDriverResponseState: ApiResponseState.NULL,
        schedulesByDriver: [] as ISchedule[],
        failureSchedulesByDriver: { message: "" },
    }),

    actions: {
        async getCompletedTripsTrend(shuttleId: string, startDate?: string, endDate?: string, interval: string = 'day'): Promise<TrendData | null> {
            return new Promise<TrendData | null>(async (resolve) => {
                try {
                    this.apiCompletedTripsTrendResponseState = ApiResponseState.LOADING;

                    const params: Record<string, string> = {
                        interval
                    };

                    if (shuttleId) {
                        params.shuttle_id = shuttleId;
                    }

                    if (startDate) {
                        params.start_date = startDate;
                    }

                    if (endDate) {
                        params.end_date = endDate;
                    }

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/schedules/trend/completed`, {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                        params
                    });

                    // Handle errors
                    if (error.value) {
                        console.error("COMPLETED TRIPS TREND ERROR:", error.value);
                        this.apiCompletedTripsTrendResponseState = ApiResponseState.FAILED;
                        this.failureCompletedTripsTrend.message = error.value.data?.error || 'Server error! Could not get completed trips trend';
                        return resolve(null);
                    }

                    // Success
                    const responseData = data.value as any;
                    if (responseData && responseData.trendData) {
                        this.completedTripsTrend = responseData.trendData;
                        this.apiCompletedTripsTrendResponseState = ApiResponseState.SUCCESS;
                        resolve(this.completedTripsTrend);
                    } else {
                        this.apiCompletedTripsTrendResponseState = ApiResponseState.FAILED;
                        this.failureCompletedTripsTrend.message = 'Invalid response format';
                        resolve(null);
                    }
                } catch (error) {
                    console.error("Error getting completed trips trend:", error);
                    this.apiCompletedTripsTrendResponseState = ApiResponseState.FAILED;
                    this.failureCompletedTripsTrend.message = 'Internal Server error! Could not get completed trips trend';
                    resolve(null);
                }
            });
        },

        async getSchedulesByShuttle(shuttleId: string): Promise<ISchedule[]> {
            return new Promise<ISchedule[]>(async (resolve) => {
                try {
                    this.apiSchedulesByShuttleResponseState = ApiResponseState.LOADING;

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/schedules`, {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                        params: {
                            shuttle_id: shuttleId
                        }
                    });

                    // Handle errors
                    if (error.value) {
                        console.error("SCHEDULES BY SHUTTLE ERROR:", error.value);
                        this.apiSchedulesByShuttleResponseState = ApiResponseState.FAILED;
                        this.failureSchedulesByShuttle.message = error.value.data?.error || 'Server error! Could not get schedules';
                        return resolve([]);
                    }

                    // Success
                    const schedules = Array.isArray(data.value) ? data.value : [];
                    this.schedulesByShuttle = schedules;
                    this.apiSchedulesByShuttleResponseState = ApiResponseState.SUCCESS;
                    resolve(schedules);

                } catch (error) {
                    console.error("Error getting schedules by shuttle:", error);
                    this.apiSchedulesByShuttleResponseState = ApiResponseState.FAILED;
                    this.failureSchedulesByShuttle.message = 'Internal Server error! Could not get schedules';
                    resolve([]);
                }
            });
        },

        async getSchedulesByDriver(driverId: string): Promise<ISchedule[]> {
            return new Promise<ISchedule[]>(async (resolve) => {
                try {
                    this.apiSchedulesByDriverResponseState = ApiResponseState.LOADING;

                    const { data, error } = await useFetch(`${useRuntimeConfig().public.API_BASE_URL}/schedules`, {
                        method: 'GET',
                        headers: {
                            "Authorization": `Bearer ${useUserStore().token}`
                        },
                        params: {
                            driver_id: driverId
                        }
                    });

                    // Handle errors
                    if (error.value) {
                        console.error("SCHEDULES BY DRIVER ERROR:", error.value);
                        this.apiSchedulesByDriverResponseState = ApiResponseState.FAILED;
                        this.failureSchedulesByDriver.message = error.value.data?.error || 'Server error! Could not get driver schedules';
                        return resolve([]);
                    }

                    // Success
                    const schedules = Array.isArray(data.value) ? data.value : [];
                    this.schedulesByDriver = schedules;
                    this.apiSchedulesByDriverResponseState = ApiResponseState.SUCCESS;
                    resolve(schedules);

                } catch (error) {
                    console.error("Error getting schedules by driver:", error);
                    this.apiSchedulesByDriverResponseState = ApiResponseState.FAILED;
                    this.failureSchedulesByDriver.message = 'Internal Server error! Could not get driver schedules';
                    resolve([]);
                }
            });
        },
    },

    getters: {
        // COMPLETED TRIPS TREND
        failedCompletedTripsTrend: (state) => state.apiCompletedTripsTrendResponseState === ApiResponseState.FAILED,
        successCompletedTripsTrend: (state) => state.apiCompletedTripsTrendResponseState === ApiResponseState.SUCCESS,
        isLoadingCompletedTripsTrend: (state) => state.apiCompletedTripsTrendResponseState === ApiResponseState.LOADING,
        hasCompletedTripsTrend: (state) => state.apiCompletedTripsTrendResponseState === ApiResponseState.SUCCESS && state.completedTripsTrend !== null,

        // SCHEDULES BY SHUTTLE
        failedSchedulesByShuttle: (state) => state.apiSchedulesByShuttleResponseState === ApiResponseState.FAILED,
        successSchedulesByShuttle: (state) => state.apiSchedulesByShuttleResponseState === ApiResponseState.SUCCESS,
        isLoadingSchedulesByShuttle: (state) => state.apiSchedulesByShuttleResponseState === ApiResponseState.LOADING,
        hasSchedulesByShuttle: (state) => state.apiSchedulesByShuttleResponseState === ApiResponseState.SUCCESS && state.schedulesByShuttle.length > 0,

        // SCHEDULES BY DRIVER
        failedSchedulesByDriver: (state) => state.apiSchedulesByDriverResponseState === ApiResponseState.FAILED,
        successSchedulesByDriver: (state) => state.apiSchedulesByDriverResponseState === ApiResponseState.SUCCESS,
        isLoadingSchedulesByDriver: (state) => state.apiSchedulesByDriverResponseState === ApiResponseState.LOADING,
        hasSchedulesByDriver: (state) => state.apiSchedulesByDriverResponseState === ApiResponseState.SUCCESS && state.schedulesByDriver.length > 0,
    }
});
