import { ref, computed, reactive } from 'vue';
import { format, subDays, parseISO } from 'date-fns';

export interface FilterConfig {
    search: string;
    status: string;
    dateRange: { start: Date; end: Date } | null;
    [key: string]: any;
}

export interface AnalyticsData {
    [key: string]: any;
}

export function useAnalytics<T extends AnalyticsData>(data: Ref<T[] | null>) {
    // Filter state
    const currentFilters = ref<FilterConfig>({
        search: '',
        status: '',
        dateRange: null
    });

    // Date range helpers
    const getDefaultDateRange = (days: number = 30) => ({
        start: subDays(new Date(), days),
        end: new Date()
    });

    // Generic filter function
    const applyFilters = (items: T[], filters: FilterConfig): T[] => {
        let filtered = [...items];

        // Apply search filter
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filtered = filtered.filter(item => {
                // Search in common fields
                const searchableFields = ['name', 'title', 'reference', 'email', 'first_name', 'last_name'];
                return searchableFields.some(field => {
                    const value = item[field];
                    if (typeof value === 'string') {
                        return value.toLowerCase().includes(searchTerm);
                    }
                    return false;
                });
            });
        }

        // Apply status filter
        if (filters.status) {
            filtered = filtered.filter(item => 
                item.status?.toLowerCase() === filters.status.toLowerCase()
            );
        }

        // Apply date range filter
        if (filters.dateRange) {
            const { start, end } = filters.dateRange;
            filtered = filtered.filter(item => {
                // Try common date fields
                const dateFields = ['created_at', 'updated_at', 'booking_schedule_date', 'departure_time'];
                const itemDate = dateFields.find(field => item[field]);
                if (itemDate) {
                    const date = new Date(item[itemDate]);
                    return date >= start && date <= end;
                }
                return true;
            });
        }

        return filtered;
    };

    // Filtered data
    const filteredData = computed(() => {
        if (!data.value) return [];
        return applyFilters(data.value, currentFilters.value);
    });

    // Handle filter changes
    const handleFiltersChanged = (filters: FilterConfig) => {
        currentFilters.value = { ...currentFilters.value, ...filters };
    };

    // Clear all filters
    const clearFilters = () => {
        currentFilters.value = {
            search: '',
            status: '',
            dateRange: null
        };
    };

    // Calculate basic metrics
    const calculateMetrics = (items: T[]) => {
        const total = items.length;
        const today = new Date().toDateString();
        const todayItems = items.filter(item => {
            const dateFields = ['created_at', 'updated_at', 'booking_schedule_date'];
            const itemDate = dateFields.find(field => item[field]);
            return itemDate && new Date(item[itemDate]).toDateString() === today;
        });

        return {
            total,
            today: todayItems.length,
            growth: 12.5, // Mock growth - in real app, calculate from historical data
        };
    };

    // Calculate revenue metrics (for items with amount/price fields)
    const calculateRevenueMetrics = (items: T[]) => {
        const revenueFields = ['total_amount', 'amount', 'price'];
        const totalRevenue = items.reduce((sum, item) => {
            const revenueField = revenueFields.find(field => item[field] !== undefined);
            return sum + (revenueField ? Number(item[revenueField]) || 0 : 0);
        }, 0);

        const averageValue = items.length > 0 ? totalRevenue / items.length : 0;

        return {
            totalRevenue,
            averageValue,
            revenueGrowth: 8.3 // Mock growth
        };
    };

    // Calculate status distribution
    const calculateStatusDistribution = (items: T[]) => {
        const statusCounts = items.reduce((acc, item) => {
            const status = item.status?.toLowerCase() || 'unknown';
            acc[status] = (acc[status] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        return Object.entries(statusCounts).map(([status, count]) => ({
            status,
            count,
            percentage: items.length > 0 ? (count / items.length) * 100 : 0
        }));
    };

    // Generate trend data
    const generateTrendData = (items: T[], days: number = 30) => {
        const dateRange = Array.from({ length: days }, (_, i) => {
            const date = subDays(new Date(), days - 1 - i);
            return format(date, 'yyyy-MM-dd');
        });

        return dateRange.map(date => {
            const dayItems = items.filter(item => {
                const dateFields = ['created_at', 'updated_at', 'booking_schedule_date'];
                const itemDate = dateFields.find(field => item[field]);
                return itemDate && format(new Date(item[itemDate]), 'yyyy-MM-dd') === date;
            });

            const revenueFields = ['total_amount', 'amount', 'price'];
            const dayRevenue = dayItems.reduce((sum, item) => {
                const revenueField = revenueFields.find(field => item[field] !== undefined);
                return sum + (revenueField ? Number(item[revenueField]) || 0 : 0);
            }, 0);

            return {
                date,
                count: dayItems.length,
                revenue: dayRevenue
            };
        });
    };

    // Export functionality
    const exportToCSV = (items: T[], filename: string) => {
        if (items.length === 0) return;

        const headers = Object.keys(items[0]);
        const csvContent = [
            headers.join(','),
            ...items.map(item => 
                headers.map(header => {
                    const value = item[header];
                    // Handle nested objects and arrays
                    if (typeof value === 'object' && value !== null) {
                        return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
                    }
                    return `"${String(value || '').replace(/"/g, '""')}"`;
                }).join(',')
            )
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `${filename}-${format(new Date(), 'yyyy-MM-dd')}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return {
        // State
        currentFilters,
        filteredData,

        // Methods
        handleFiltersChanged,
        clearFilters,
        calculateMetrics,
        calculateRevenueMetrics,
        calculateStatusDistribution,
        generateTrendData,
        exportToCSV,
        getDefaultDateRange,

        // Computed
        totalCount: computed(() => data.value?.length || 0),
        filteredCount: computed(() => filteredData.value.length),
        hasFilters: computed(() => {
            return !!(currentFilters.value.search || 
                     currentFilters.value.status || 
                     currentFilters.value.dateRange);
        })
    };
}

// Color utilities for consistent theming
export const getStatusColor = (status: string) => {
    const statusColors = {
        'active': 'bg-green-100 text-green-800',
        'confirmed': 'bg-green-100 text-green-800',
        'success': 'bg-green-100 text-green-800',
        'completed': 'bg-blue-100 text-blue-800',
        'pending': 'bg-yellow-100 text-yellow-800',
        'cancelled': 'bg-red-100 text-red-800',
        'inactive': 'bg-gray-100 text-gray-800',
        'refunded': 'bg-gray-100 text-gray-800'
    };
    return statusColors[status.toLowerCase() as keyof typeof statusColors] || 'bg-gray-100 text-gray-800';
};

export const getMetricIconConfig = (type: string) => {
    const configs = {
        'total': { bg: 'bg-blue-50', color: 'text-blue-600' },
        'revenue': { bg: 'bg-green-50', color: 'text-green-600' },
        'average': { bg: 'bg-purple-50', color: 'text-purple-600' },
        'rate': { bg: 'bg-orange-50', color: 'text-orange-600' },
        'growth': { bg: 'bg-indigo-50', color: 'text-indigo-600' }
    };
    return configs[type] || { bg: 'bg-gray-50', color: 'text-gray-600' };
};
