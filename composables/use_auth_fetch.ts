import type { FetchOptions } from 'ofetch'
import { useUserStore } from '~/stores/auth/user/user.store'

/**
 * Auth-aware wrapper around $fetch that automatically:
 * - injects the Authorization header from the current access token
 * - on 401, attempts to refresh the token and retries the request once
 */
export async function useAuthFetch<T>(
  path: string,
  options?: FetchOptions
): Promise<T> {
  const runtimeConfig = useRuntimeConfig()
  const userStore = useUserStore()

  const buildUrl = (p: string) => {
    if (!p) return runtimeConfig.public.API_BASE_URL || ''
    // If absolute or internal Nitro API path, return as-is
    if (p.startsWith('http://') || p.startsWith('https://') || p.startsWith('/api/')) {
      return p
    }
    const base = runtimeConfig.public.API_BASE_URL || ''
    return `${base}${p.startsWith('/') ? '' : '/'}${p}`
  }

  const withAuthHeaders = (opts?: FetchOptions): FetchOptions => {
    const headers = {
      ...(opts?.headers as Record<string, string> | undefined),
      ...(userStore.token ? { Authorization: `Bearer ${userStore.token}` } : {}),
    }
    return { ...opts, headers }
  }

  const url = buildUrl(path)

  try {
    return await $fetch<T>(url, withAuthHeaders(options))
  } catch (error: any) {
    // If unauthorized, attempt a refresh and retry once
    const isUnauthorized = error?.status === 401
    if (isUnauthorized) {
      const refreshed = await userStore.refreshAccessToken()
      if (refreshed) {
        return await $fetch<T>(url, withAuthHeaders(options))
      }
    }
    throw error
  }
}


