<template>
    <div v-if="activeSchedule" class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4 mb-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Currently Active Trip</h3>
                    <p class="text-sm text-gray-600">Driver is currently on this scheduled trip</p>
                </div>
            </div>
            <Badge class="bg-green-500 text-white">
                {{ activeSchedule.status.toUpperCase() }}
            </Badge>
        </div>
        
        <div class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-white rounded-lg p-3 shadow-sm">
                <div class="text-xs font-medium text-gray-500 uppercase tracking-wide">Route</div>
                <div class="mt-1 text-sm font-semibold text-gray-900">{{ activeSchedule.route_id.name }}</div>
            </div>
            
            <div class="bg-white rounded-lg p-3 shadow-sm">
                <div class="text-xs font-medium text-gray-500 uppercase tracking-wide">Shuttle</div>
                <div class="mt-1 text-sm font-semibold text-gray-900">
                    {{ activeSchedule.shuttle_id.make }} {{ activeSchedule.shuttle_id.model }}
                </div>
                <div class="text-xs text-gray-500">{{ activeSchedule.shuttle_id.plate_number }}</div>
            </div>
            
            <div class="bg-white rounded-lg p-3 shadow-sm">
                <div class="text-xs font-medium text-gray-500 uppercase tracking-wide">Departure</div>
                <div class="mt-1 text-sm font-semibold text-gray-900">
                    {{ useFormatDateHuman(new Date(activeSchedule.departure_time)) }}
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-3 shadow-sm">
                <div class="text-xs font-medium text-gray-500 uppercase tracking-wide">Arrival</div>
                <div class="mt-1 text-sm font-semibold text-gray-900">
                    {{ useFormatDateHuman(new Date(activeSchedule.arrival_time)) }}
                </div>
            </div>
        </div>
        
        <div class="mt-4 flex items-center justify-between">
            <div class="flex items-center space-x-4 text-sm text-gray-600">
                <span>Available Seats: <strong>{{ activeSchedule.available_seats }}</strong></span>
                <span>Price: <strong>${{ activeSchedule.price.toFixed(2) }}</strong></span>
            </div>
            <div class="text-xs text-gray-500">
                Last updated: {{ useFormatDateHuman(new Date()) }}
            </div>
        </div>
    </div>
    
    <div v-else-if="!isLoading" class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
            </div>
            <div>
                <h3 class="text-lg font-medium text-gray-700">No Active Trip</h3>
                <p class="text-sm text-gray-500">Driver is currently not assigned to any active trip</p>
            </div>
        </div>
    </div>
    
    <div v-else class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <Loader2 class="w-4 h-4 animate-spin text-gray-400" />
            </div>
            <div>
                <h3 class="text-lg font-medium text-gray-700">Loading Active Trip...</h3>
                <p class="text-sm text-gray-500">Checking driver's current status</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useScheduleStore, type ISchedule } from '~/stores/schedule/schedule.store';
import type { User } from '~/stores/auth/user/model/user.model';
import { Badge } from '@/components/ui/badge';
import { Loader2 } from 'lucide-vue-next';

const props = defineProps<{ driver: User }>();

const scheduleStore = useScheduleStore();
const isLoading = ref(true);
const driverSchedules = ref<ISchedule[]>([]);

// Find the active schedule for the driver
const activeSchedule = computed(() => {
    return driverSchedules.value.find(schedule => 
        schedule.status === 'active' || schedule.status === 'in_progress'
    );
});

// Fetch driver schedules to find active trip
const fetchDriverSchedules = async () => {
    isLoading.value = true;
    try {
        const schedules = await scheduleStore.getSchedulesByDriver(props.driver.id);
        driverSchedules.value = schedules;
    } catch (error) {
        console.error('Error fetching driver schedules:', error);
        driverSchedules.value = [];
    } finally {
        isLoading.value = false;
    }
};

// Watch for driver changes
watch(() => props.driver, () => {
    if (props.driver) {
        fetchDriverSchedules();
    }
}, { immediate: true });

// Load data on component mount
onMounted(() => {
    if (props.driver) {
        fetchDriverSchedules();
    }
});
</script>
