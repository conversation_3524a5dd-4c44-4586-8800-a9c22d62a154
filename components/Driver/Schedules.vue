<template>
    <div class="border-gray-100 bg-white rounded-lg p-5">
        <div class="flex justify-between">
            <h1 class="font-bold">Driver Schedules</h1>
        </div>
        <div class="mt-2">
            <DataTable class="h-full" :columns="columns" :data="data ?? []" :is-loading="pending"
                @get-table-data="handleDataTableData" @get-row-data="handleRowClicked">
                <template #dataTableSearch>
                    <Input v-if="dataTableRef" class="w-1/3" type="search" placeholder="Search schedules..."
                        @input="dataTableRef.setGlobalFilter($event.target.value)" />
                </template>
            </DataTable>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Checkbox } from '@/components/ui/checkbox';
import type { ColumnDef, Row, Table } from '@tanstack/vue-table';
import { useScheduleStore, type ISchedule } from '~/stores/schedule/schedule.store';
import type { User } from '~/stores/auth/user/model/user.model';
import { Badge } from '@/components/ui/badge';

const props = defineProps<{ driver: User }>();

const scheduleStore = useScheduleStore();

// Get schedules by driver
const { pending, data } = await useAsyncData<ISchedule[]>(
    `schedulesByDriver-${props.driver.id}`, 
    () => scheduleStore.getSchedulesByDriver(props.driver.id), 
    { lazy: true }
)

// DATA TABLE STUFF
const selectedRow = ref<Row<ISchedule>>()
const dataTableRef = ref<Table<ISchedule>>()

const handleDataTableData = (data: Table<ISchedule>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<ISchedule>) => {
    // Set selected row
    selectedRow.value = row;
    console.log('Selected schedule:', row.original);
}

const columns: ColumnDef<ISchedule>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllPageRowsSelected(),
            'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => row.toggleSelected(!!value),
            'ariaLabel': 'Select row',  
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'route_id.name',
        header: 'Route',
    },
    {
        header: 'Shuttle',
        cell: ({ row }) => `${row.original.shuttle_id.make} ${row.original.shuttle_id.model} (${row.original.shuttle_id.plate_number})`,
    },
    {
        accessorKey: 'departure_time',
        header: 'Departure Time',
        cell: ({ row }) => useFormatDateHuman(new Date(row.original.departure_time)),
    },
    {
        accessorKey: 'arrival_time',
        header: 'Arrival Time',
        cell: ({ row }) => useFormatDateHuman(new Date(row.original.arrival_time)),
    },
    {
        accessorKey: 'available_seats',
        header: 'Available Seats',
    },
    {
        accessorKey: 'price',
        header: 'Price',
        cell: ({ row }) => `$${row.original.price.toFixed(2)}`,
    },
    {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => h('div', { class: 'flex space-x-2' }, [
            h(Badge, {
                variant: 'secondary',
                class: `${row.original.status === 'active' ? 'bg-green-400' : 
                         row.original.status === 'completed' ? 'bg-blue-400' : 
                         row.original.status === 'cancelled' ? 'bg-red-400' : 'bg-gray-400'} text-black`
            }, row.original.status),
        ]),
    },
]
</script>
