<template>
    <div class="border-gray-100 bg-white rounded-lg p-5">
        <div class="flex justify-between">
            <h1 class="font-bold">All Trips</h1>
        </div>
        <div class="mt-2">
            <DataTable class="h-full" :columns="columns" :data="bookingsData ?? []" :is-loading="pending"
                @get-table-data="handleDataTableData" @get-row-data="handleRowClicked">
                <template #dataTableSearch>
                    <Input v-if="dataTableRef" class="w-1/3" type="search" placeholder="Search trips..."
                        @input="dataTableRef.setGlobalFilter($event.target.value)" />
                </template>
            </DataTable>
        </div>
    </div>
</template>
<script setup lang="ts">
import type { ColumnDef, Row, Table } from '@tanstack/vue-table'
import { Checkbox } from '@/components/ui/checkbox'
import { useBookingStore } from '~/stores/booking/booking.store';
import type { IBooking } from '~/stores/booking/model/booking.model';
import { Badge } from '@/components/ui/badge';
import type { User } from '~/stores/auth/user/model/user.model';

const props = defineProps<{driver: User}>();

const bookingStore = useBookingStore();

// Get all bookings/trips
const {pending, status, data: bookingsData, error} = await useAsyncData<IBooking[]>('allTrips', () => bookingStore.getBookings(1, 100), {lazy: true})

// DATA TABLE STUFF
const selectedRow = ref<Row<IBooking>>()
const dataTableRef = ref<Table<IBooking>>()

const handleDataTableData = (data: Table<IBooking>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<IBooking>) => {
    // Set selected row
    selectedRow.value = row;
    // You can add additional logic here if needed
}


const columns: ColumnDef<IBooking>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllPageRowsSelected(),
            'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => row.toggleSelected(!!value),
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'booking_reference',
        header: 'Booking Ref',
    },
    {
        accessorKey: 'booking_schedule_date',
        header: 'Trip Date',
        cell: ({ row }) => useFormatDateHuman(new Date(row.original.booking_schedule_date)),
    },
    {
        accessorKey: 'route.origin.name',
        header: 'Origin',
    },
    {
        accessorKey: 'route.destination.name',
        header: 'Destination',
    },
    {
        header: 'Rider',
        cell: ({ row }) => `${row.original.rider.first_name} ${row.original.rider.last_name}`,
    },
    {
        accessorKey: 'number_of_tickets',
        header: 'Tickets',
    },
    {
        accessorKey: 'total_amount',
        header: 'Amount',
        cell: ({ row }) => `$${row.original.total_amount.toFixed(2)}`,
    },
    {
        accessorKey: 'departure_time',
        header: 'Departure',
    },
    {
        accessorKey: 'arrival_time',
        header: 'Arrival',
    },
    {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => h('div', { class: 'flex space-x-2' }, [
            h(Badge, {
                variant: 'secondary',
                class: `${row.original.status === 'confirmed' ? 'bg-green-400' :
                         row.original.status === 'pending' ? 'bg-yellow-400' :
                         row.original.status === 'cancelled' ? 'bg-red-400' : 'bg-gray-400'} text-black`
            }, row.original.status)
        ]),
    },
]
// end of DATA TABLE STUFF
</script>