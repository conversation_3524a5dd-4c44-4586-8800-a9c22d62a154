<template>
    <AnalyticsBaseInsightsSidebar
        title="Driver Insights"
        subtitle="Real-time driver analytics and performance"
        :is-sheet="isSheet"
        :today-stats="todayStats"
        :alerts="alerts"
        :recent-activity="recentActivity"
        :quick-actions="quickActions"
        :performance-metrics="performanceMetrics"
        @close="$emit('close')"
        @quick-action="handleQuickAction"
    />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    AlertTriangle, 
    Info, 
    Plus, 
    Minus, 
    Settings,
    Download,
    FileText,
    UserCheck,
    Calendar
} from 'lucide-vue-next';
import type { User } from '~/stores/auth/user/model/user.model';
import type {
    TodayStatConfig,
    AlertConfig,
    ActivityConfig,
    QuickActionConfig,
    PerformanceMetricConfig
} from '~/components/Analytics/BaseInsightsSidebar.vue';

const props = defineProps<{
    drivers: User[];
    isSheet?: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
}>();

// Today's stats
const todayStats = computed<TodayStatConfig[]>(() => {
    const totalDrivers = props.drivers.length;
    const activeDrivers = props.drivers.filter(d => d.role?.name?.toLowerCase() === 'driver').length;
    
    return [
        {
            key: 'total',
            label: 'Total Drivers',
            value: totalDrivers,
            color: 'bg-blue-500'
        },
        {
            key: 'active',
            label: 'Active Today',
            value: Math.floor(activeDrivers * 0.8), // Mock active drivers
            color: 'bg-green-500'
        },
        {
            key: 'scheduled',
            label: 'Scheduled Trips',
            value: Math.floor(activeDrivers * 1.5), // Mock scheduled trips
            color: 'bg-purple-500'
        },
        {
            key: 'completion',
            label: 'Completion Rate',
            value: 94.2,
            format: 'percentage',
            color: 'bg-orange-500'
        }
    ];
});

// Alerts
const alerts = computed<AlertConfig[]>(() => {
    const alertsList: AlertConfig[] = [];
    
    // Mock alerts based on driver data
    if (props.drivers.length > 50) {
        alertsList.push({
            id: 'capacity',
            type: 'info',
            title: 'High Driver Volume',
            message: `${props.drivers.length} drivers in system - consider fleet optimization`,
            time: '30 minutes ago',
            icon: Info
        });
    }
    
    // Mock performance alert
    alertsList.push({
        id: 'performance',
        type: 'warning',
        title: 'Driver Performance Review',
        message: '3 drivers require performance review this week',
        time: '2 hours ago',
        icon: AlertTriangle
    });
    
    return alertsList;
});

// Recent activity
const recentActivity = computed<ActivityConfig[]>(() => [
    {
        id: 1,
        message: 'Driver John D. completed trip to Airport',
        time: '3 min ago',
        icon: Plus,
        iconColor: 'bg-green-100 text-green-600'
    },
    {
        id: 2,
        message: 'New driver Sarah M. onboarded',
        time: '15 min ago',
        icon: UserCheck,
        iconColor: 'bg-blue-100 text-blue-600'
    },
    {
        id: 3,
        message: 'Driver Mike R. scheduled for training',
        time: '1 hour ago',
        icon: Calendar,
        iconColor: 'bg-yellow-100 text-yellow-600'
    }
]);

// Quick actions
const quickActions = computed<QuickActionConfig[]>(() => [
    {
        key: 'export',
        label: 'Export Driver Data',
        icon: Download
    },
    {
        key: 'report',
        label: 'Generate Performance Report',
        icon: FileText
    },
    {
        key: 'schedule',
        label: 'Manage Schedules',
        icon: Calendar
    },
    {
        key: 'settings',
        label: 'Driver Settings',
        icon: Settings
    }
]);

// Performance metrics
const performanceMetrics = computed<PerformanceMetricConfig[]>(() => [
    {
        key: 'availability',
        label: 'Driver Availability',
        value: 87.3,
        format: 'percentage',
        color: 'bg-green-500',
        percentage: 87.3
    },
    {
        key: 'response_time',
        label: 'Avg Response Time',
        value: 4.2,
        format: 'number',
        color: 'bg-blue-500',
        percentage: 84 // 4.2 minutes out of 5 max
    },
    {
        key: 'satisfaction',
        label: 'Customer Satisfaction',
        value: 4.6,
        format: 'number',
        color: 'bg-purple-500',
        percentage: 92 // 4.6 out of 5
    }
]);

// Handle quick actions
const handleQuickAction = (actionKey: string) => {
    switch (actionKey) {
        case 'export':
            console.log('Exporting driver data...');
            break;
        case 'report':
            console.log('Generating performance report...');
            break;
        case 'schedule':
            console.log('Opening schedule management...');
            break;
        case 'settings':
            console.log('Opening driver settings...');
            break;
    }
};
</script>
