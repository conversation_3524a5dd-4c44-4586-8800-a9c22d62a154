<template>
    <section class="p-2 flex flex-col gap-2">
        <!-- Active Trip Indicator -->
        <DriverActiveTrip :driver="driver"></DriverActiveTrip>

        <!-- Driver Details -->
        <DriverDetails :driver="driver"></DriverDetails>

        <!-- Trips Trend Chart -->
        <div class="overflow-hidden bg-white border border-gray-200 rounded-xl col-span-12">
            <DriverTripsChart :driver="driver"></DriverTripsChart>
        </div>

        <!-- Driver Schedules -->
        <DriverSchedules :driver="driver"></DriverSchedules>

        <!-- All Trips Table -->
        <DriverTrips :driver="driver"></DriverTrips>
    </section>
</template>
<script setup lang="ts">
import type { User } from '~/stores/auth/user/model/user.model';


const props = defineProps<{ driver: User }>();

</script>