<template>
    <div class="w-full max-h-[340px] h-full bg-white rounded-xl p-5 flex flex-col gap-2">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="font-bold text-lg">{{ 'Completed Trips Trend' }}</h1>
                <p class="text-xs text-muted-foreground">{{ 'Number of trips completed by driver' }}</p>
            </div>
            <DateRangePicker v-if="!isLoading" @handle-date-change="onDateChanged"></DateRangePicker>
        </div>
        <div v-if="isLoading" class="w-full h-full flex items-center justify-center">
            <Loader2 class="h-8 w-8 animate-spin text-primary" />
        </div>
        <div v-else-if="error" class="w-full h-full flex items-center justify-center">
            <div class="text-center">
                <p class="text-red-500">{{ error }}</p>
                <Button variant="outline" size="sm" class="mt-2" @click="fetchTrendData">Retry</Button>
            </div>
        </div>
        <template v-else>
            <ClientOnly>
                <apexchart :key="chartOptions.series" height="100%" width="100%" :options="chartOptions"
                    :series="chartOptions.series">
                </apexchart>
            </ClientOnly>
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { Loader2 } from 'lucide-vue-next';
import { useScheduleStore } from '~/stores/schedule/schedule.store';
import type { TrendData } from '~/stores/schedule/schedule.store';
import { format, subDays } from 'date-fns';
import type { User } from '~/stores/auth/user/model/user.model';

const props = defineProps<{
    driver: User;
}>();

const scheduleStore = useScheduleStore();
const isLoading = ref(false);
const error = ref<string | null>(null);
const trendData = ref<TrendData | null>(null);

// Date range for filtering
const startDate = ref(format(subDays(new Date(), 30), 'yyyy-MM-dd'));
const endDate = ref(format(new Date(), 'yyyy-MM-dd'));

// Fetch trend data - for now we'll try to use the existing API with driver info
// In a real implementation, you might need a driver-specific endpoint
const fetchTrendData = async () => {
    isLoading.value = true;
    error.value = null;

    try {
        // For now, we'll show a placeholder message since we don't have a driver-specific endpoint
        // In a real implementation, you would call something like:
        // const data = await scheduleStore.getDriverCompletedTripsTrend(props.driver.id, startDate.value, endDate.value, 'day');

        // Simulate loading time
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Create mock data for demonstration
        const mockData: TrendData = {
            trend: [
                { date: format(subDays(new Date(), 7), 'yyyy-MM-dd'), count: 3 },
                { date: format(subDays(new Date(), 6), 'yyyy-MM-dd'), count: 5 },
                { date: format(subDays(new Date(), 5), 'yyyy-MM-dd'), count: 2 },
                { date: format(subDays(new Date(), 4), 'yyyy-MM-dd'), count: 7 },
                { date: format(subDays(new Date(), 3), 'yyyy-MM-dd'), count: 4 },
                { date: format(subDays(new Date(), 2), 'yyyy-MM-dd'), count: 6 },
                { date: format(subDays(new Date(), 1), 'yyyy-MM-dd'), count: 8 },
                { date: format(new Date(), 'yyyy-MM-dd'), count: 3 }
            ],
            total: 38,
            period: 'day',
            filter: 'completed'
        };

        trendData.value = mockData;
    } catch (err) {
        console.error('Error fetching driver trend data:', err);
        error.value = 'Failed to load trend data';
    } finally {
        isLoading.value = false;
    }
};

// Handle date range changes
const onDateChanged = (dates: { start: Date; end: Date }) => {
    startDate.value = format(dates.start, 'yyyy-MM-dd');
    endDate.value = format(dates.end, 'yyyy-MM-dd');
    fetchTrendData();
};

// Process chart data
const chartData = computed(() => {
    if (!trendData.value) return [];

    return trendData.value.trend.map(item => ({
        x: item.date,
        y: item.count
    }));
});

// Chart options
const chartOptions = computed(() => ({
    chart: {
        type: 'area',
        height: 250,
        toolbar: {
            show: false,
        },
        zoom: {
            enabled: false,
        },
    },
    series: [{
        name: 'Completed Trips',
        data: chartData.value
    }],
    dataLabels: {
        enabled: false,
    },
    stroke: {
        show: true,
        curve: 'smooth',
        lineCap: 'butt',
        colors: undefined,
        width: 2,
    },
    xaxis: {
        type: 'datetime',
        labels: {
            format: 'dd MMM',
        }
    },
    yaxis: {
        show: true,
        title: {
            text: 'Trips',
        },
        min: 0,
        forceNiceScale: true,
    },
    tooltip: {
        enabled: true,
        x: {
            format: 'dd MMM yyyy'
        }
    },
    fill: {
        opacity: 0.5,
    },
    colors: ['#00E396'],
    grid: {
        show: true,
        yaxis: {
            lines: {
                show: true,
            }
        }
    },
    legend: {
        show: true,
        position: 'top',
        horizontalAlign: 'center',
    },
}));

// Watch for changes in driver
watch(() => props.driver, () => {
    if (props.driver) {
        fetchTrendData();
    }
}, { immediate: true });

// Load data on component mount
onMounted(() => {
    if (props.driver) {
        fetchTrendData();
    }
});

</script>