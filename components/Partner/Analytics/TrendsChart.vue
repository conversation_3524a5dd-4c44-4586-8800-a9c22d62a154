<template>
    <AnalyticsBaseTrendsChart
        title="Partner Performance & Revenue Analytics"
        subtitle="Track partner revenue, commission trends, and partnership growth"
        :chart-types="chartTypes"
        :data="chartData"
        :is-loading="isLoading"
        :error="error"
        :summary-stats="summaryStats"
        :colors="['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6']"
        @chart-type-changed="onChartTypeChanged"
        @date-range-changed="onDateRangeChanged"
        @retry="fetchTrendData"
    />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { format, subDays } from 'date-fns';
import type { IPartner } from '~/stores/partner/model/partner.model';
import type { ChartType, SummaryStatConfig, ChartDataPoint } from '~/components/Analytics/BaseTrendsChart.vue';

const props = defineProps<{
    partners: IPartner[];
}>();

const isLoading = ref(false);
const error = ref<string | null>(null);
const currentChartType = ref('revenue');

// Chart types
const chartTypes: ChartType[] = [
    { key: 'revenue', label: 'Partner Revenue' },
    { key: 'commission', label: 'Commission Paid' },
    { key: 'growth', label: 'Partner Growth' },
    { key: 'satisfaction', label: 'Satisfaction Score' }
];

// Generate mock trend data (in real app, fetch from API)
const generateTrendData = (type: string): ChartDataPoint[] => {
    return Array.from({ length: 30 }, (_, i) => {
        const date = format(subDays(new Date(), 29 - i), 'yyyy-MM-dd');
        let value = 0;
        
        switch (type) {
            case 'revenue':
                value = Math.floor(Math.random() * 8000) + 3000; // $3000-11000 daily revenue
                break;
            case 'commission':
                value = Math.floor(Math.random() * 800) + 300; // $300-1100 daily commission
                break;
            case 'growth':
                value = Math.floor(Math.random() * 5) + 1; // 1-6 new partners per day
                break;
            case 'satisfaction':
                value = (Math.random() * 1.5 + 3.5); // 3.5-5.0 satisfaction score
                break;
        }
        
        return { x: date, y: value };
    });
};

const chartData = computed(() => generateTrendData(currentChartType.value));

// Summary statistics
const summaryStats = computed<SummaryStatConfig[]>(() => {
    const data = chartData.value;
    const values = data.map(d => d.y);
    
    const total = values.reduce((sum, val) => sum + val, 0);
    const average = values.length > 0 ? total / values.length : 0;
    const peak = values.length > 0 ? Math.max(...values) : 0;
    const growth = 15.7; // Mock growth
    
    let format: 'currency' | 'number' | 'percentage' = 'number';
    if (currentChartType.value === 'revenue' || currentChartType.value === 'commission') {
        format = 'currency';
    }
    
    const currentTypeLabel = chartTypes.find(t => t.key === currentChartType.value)?.label || 'Data';
    
    return [
        {
            key: 'total_partners',
            label: 'Total Partners',
            value: props.partners.length,
            format: 'number'
        },
        {
            key: 'average',
            label: `Avg ${currentTypeLabel}`,
            value: Math.round(average * 100) / 100,
            format
        },
        {
            key: 'peak',
            label: 'Peak Performance',
            value: Math.round(peak * 100) / 100,
            format
        },
        {
            key: 'growth',
            label: 'Growth Rate',
            value: growth,
            format: 'percentage'
        }
    ];
});

// Event handlers
const onChartTypeChanged = (type: string) => {
    currentChartType.value = type;
};

const onDateRangeChanged = (dates: { start: Date; end: Date }) => {
    // In real app, refetch data for new date range
    console.log('Date range changed:', dates);
    fetchTrendData();
};

const fetchTrendData = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        // Data is already computed reactively
    } catch (err) {
        error.value = 'Failed to load partner trend data';
    } finally {
        isLoading.value = false;
    }
};

onMounted(() => {
    fetchTrendData();
});
</script>
