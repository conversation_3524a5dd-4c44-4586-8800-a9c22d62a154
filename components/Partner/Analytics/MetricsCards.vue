<template>
    <AnalyticsBaseMetricsCards :metrics="partnerMetrics" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    Building2, 
    TrendingUp, 
    DollarSign, 
    Users, 
    Percent,
    Activity,
    Target,
    Star
} from 'lucide-vue-next';
import type { IPartner } from '~/stores/partner/model/partner.model';
import type { MetricConfig } from '~/components/Analytics/BaseMetricsCards.vue';

const props = defineProps<{
    partners: IPartner[];
}>();

const partnerMetrics = computed<MetricConfig[]>(() => {
    const totalPartners = props.partners.length;
    const activePartners = props.partners.filter(p => p.status?.toLowerCase() === 'active').length;
    const averageCommission = totalPartners > 0 ? 
        props.partners.reduce((sum, p) => sum + (p.commission_rate || 0), 0) / totalPartners : 0;
    
    // Mock calculations (in real app, get from bookings/revenue API)
    const totalRevenue = 125000; // Mock total partner revenue
    const averageRevenuePerPartner = totalPartners > 0 ? totalRevenue / totalPartners : 0;
    const partnerSatisfaction = 4.2; // Mock satisfaction score
    const retentionRate = 89.5; // Mock retention rate
    const newPartnersThisMonth = Math.floor(totalPartners * 0.08); // 8% new this month

    return [
        {
            key: 'total_partners',
            label: 'Total Partners',
            value: totalPartners,
            format: 'number',
            trend: 8.3,
            icon: Building2,
            iconBg: 'bg-blue-50',
            iconColor: 'text-blue-600'
        },
        {
            key: 'active_partners',
            label: 'Active Partners',
            value: activePartners,
            format: 'number',
            trend: 12.1,
            icon: Activity,
            iconBg: 'bg-green-50',
            iconColor: 'text-green-600'
        },
        {
            key: 'total_revenue',
            label: 'Total Revenue',
            value: totalRevenue,
            format: 'currency',
            trend: 15.7,
            icon: DollarSign,
            iconBg: 'bg-emerald-50',
            iconColor: 'text-emerald-600'
        },
        {
            key: 'avg_revenue_per_partner',
            label: 'Avg Revenue/Partner',
            value: averageRevenuePerPartner,
            format: 'currency',
            trend: 6.4,
            icon: Target,
            iconBg: 'bg-purple-50',
            iconColor: 'text-purple-600'
        },
        {
            key: 'avg_commission',
            label: 'Avg Commission Rate',
            value: averageCommission,
            format: 'percentage',
            trend: -1.2,
            trendLabel: 'vs last month',
            icon: Percent,
            iconBg: 'bg-orange-50',
            iconColor: 'text-orange-600'
        },
        {
            key: 'new_partners',
            label: 'New This Month',
            value: newPartnersThisMonth,
            format: 'number',
            trend: 18.9,
            icon: Users,
            iconBg: 'bg-indigo-50',
            iconColor: 'text-indigo-600'
        },
        {
            key: 'retention_rate',
            label: 'Partner Retention',
            value: retentionRate,
            format: 'percentage',
            trend: 3.8,
            icon: TrendingUp,
            iconBg: 'bg-teal-50',
            iconColor: 'text-teal-600'
        },
        {
            key: 'satisfaction_score',
            label: 'Satisfaction Score',
            value: partnerSatisfaction,
            format: 'number',
            trend: 4.2,
            trendLabel: 'out of 5.0',
            icon: Star,
            iconBg: 'bg-rose-50',
            iconColor: 'text-rose-600'
        }
    ];
});
</script>
