<template>
    <AnalyticsBaseInsightsSidebar
        title="Partner Insights"
        subtitle="Partnership analytics, revenue insights, and performance metrics"
        :is-sheet="isSheet"
        :today-stats="todayStats"
        :alerts="alerts"
        :recent-activity="recentActivity"
        :quick-actions="quickActions"
        :performance-metrics="performanceMetrics"
        @close="$emit('close')"
        @quick-action="handleQuickAction"
    />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    AlertTriangle, 
    Info, 
    Plus, 
    Minus, 
    Settings,
    Download,
    FileText,
    Building2,
    DollarSign,
    TrendingUp,
    Handshake,
    Mail
} from 'lucide-vue-next';
import type { IPartner } from '~/stores/partner/model/partner.model';
import type {
    TodayStatConfig,
    AlertConfig,
    ActivityConfig,
    QuickActionConfig,
    PerformanceMetricConfig
} from '~/components/Analytics/BaseInsightsSidebar.vue';

const props = defineProps<{
    partners: IPartner[];
    isSheet?: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
}>();

// Today's stats
const todayStats = computed<TodayStatConfig[]>(() => {
    const totalPartners = props.partners.length;
    const activePartners = props.partners.filter(p => p.status?.toLowerCase() === 'active').length;
    const averageCommission = totalPartners > 0 ? 
        props.partners.reduce((sum, p) => sum + (p.commission_rate || 0), 0) / totalPartners : 0;
    const todayRevenue = 4250; // Mock today's revenue
    
    return [
        {
            key: 'total_partners',
            label: 'Total Partners',
            value: totalPartners,
            color: 'bg-blue-500'
        },
        {
            key: 'active_partners',
            label: 'Active Partners',
            value: activePartners,
            color: 'bg-green-500'
        },
        {
            key: 'today_revenue',
            label: "Today's Revenue",
            value: todayRevenue,
            format: 'currency',
            color: 'bg-purple-500'
        },
        {
            key: 'avg_commission',
            label: 'Avg Commission',
            value: averageCommission,
            format: 'percentage',
            color: 'bg-orange-500'
        }
    ];
});

// Alerts
const alerts = computed<AlertConfig[]>(() => {
    const alertsList: AlertConfig[] = [];
    
    const highCommissionPartners = props.partners.filter(p => (p.commission_rate || 0) > 15);
    const lowCommissionPartners = props.partners.filter(p => (p.commission_rate || 0) < 5);
    
    if (highCommissionPartners.length > 0) {
        alertsList.push({
            id: 'high_commission',
            type: 'warning',
            title: 'High Commission Partners',
            message: `${highCommissionPartners.length} partner${highCommissionPartners.length > 1 ? 's' : ''} have commission rates above 15%`,
            time: '2 hours ago',
            icon: AlertTriangle
        });
    }
    
    if (lowCommissionPartners.length > 0) {
        alertsList.push({
            id: 'low_commission',
            type: 'info',
            title: 'Low Commission Opportunity',
            message: `${lowCommissionPartners.length} partner${lowCommissionPartners.length > 1 ? 's' : ''} with low commission rates - consider optimization`,
            time: '4 hours ago',
            icon: Info
        });
    }
    
    return alertsList;
});

// Recent activity
const recentActivity = computed<ActivityConfig[]>(() => [
    {
        id: 1,
        message: 'New partner "City Transport Co." onboarded',
        time: '8 min ago',
        icon: Plus,
        iconColor: 'bg-green-100 text-green-600'
    },
    {
        id: 2,
        message: 'Commission payment of $2,450 processed',
        time: '22 min ago',
        icon: DollarSign,
        iconColor: 'bg-blue-100 text-blue-600'
    },
    {
        id: 3,
        message: 'Partner "Metro Shuttles" updated commission rate',
        time: '45 min ago',
        icon: TrendingUp,
        iconColor: 'bg-purple-100 text-purple-600'
    },
    {
        id: 4,
        message: 'Partnership agreement renewed with "Express Lines"',
        time: '1 hour ago',
        icon: Handshake,
        iconColor: 'bg-teal-100 text-teal-600'
    }
]);

// Quick actions
const quickActions = computed<QuickActionConfig[]>(() => [
    {
        key: 'export_partners',
        label: 'Export Partner Data',
        icon: Download
    },
    {
        key: 'partner_report',
        label: 'Generate Partner Report',
        icon: FileText
    },
    {
        key: 'commission_review',
        label: 'Review Commissions',
        icon: DollarSign
    },
    {
        key: 'partner_outreach',
        label: 'Partner Outreach',
        icon: Mail
    }
]);

// Performance metrics
const performanceMetrics = computed<PerformanceMetricConfig[]>(() => [
    {
        key: 'revenue_growth',
        label: 'Revenue Growth',
        value: 15.7,
        format: 'percentage',
        color: 'bg-green-500',
        percentage: 78.5 // 15.7% out of 20% target
    },
    {
        key: 'partner_satisfaction',
        label: 'Partner Satisfaction',
        value: 84.0,
        format: 'percentage',
        color: 'bg-blue-500',
        percentage: 84.0 // 4.2/5.0 = 84%
    },
    {
        key: 'retention_rate',
        label: 'Partner Retention',
        value: 89.5,
        format: 'percentage',
        color: 'bg-purple-500',
        percentage: 89.5
    },
    {
        key: 'commission_efficiency',
        label: 'Commission Efficiency',
        value: 92.3,
        format: 'percentage',
        color: 'bg-orange-500',
        percentage: 92.3
    }
]);

// Handle quick actions
const handleQuickAction = (actionKey: string) => {
    switch (actionKey) {
        case 'export_partners':
            console.log('Exporting partner data...');
            break;
        case 'partner_report':
            console.log('Generating partner report...');
            break;
        case 'commission_review':
            console.log('Opening commission review...');
            break;
        case 'partner_outreach':
            console.log('Launching partner outreach...');
            break;
    }
};
</script>
