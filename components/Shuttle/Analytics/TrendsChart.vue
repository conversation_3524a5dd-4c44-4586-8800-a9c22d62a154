<template>
    <AnalyticsBaseTrendsChart
        title="Shuttle Performance Trends"
        subtitle="Track shuttle utilization and performance over time"
        :chart-types="chartTypes"
        :data="chartData"
        :is-loading="isLoading"
        :error="error"
        :summary-stats="summaryStats"
        :colors="['#3B82F6', '#10B981']"
        @chart-type-changed="onChartTypeChanged"
        @date-range-changed="onDateRangeChanged"
        @retry="fetchTrendData"
    />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { format, subDays } from 'date-fns';
import type { IShuttle } from '~/stores/shuttle/model/shuttle.model';
import type { ChartType, SummaryStatConfig, ChartDataPoint } from '~/components/Analytics/BaseTrendsChart.vue';

const props = defineProps<{
    shuttles: IShuttle[];
}>();

const isLoading = ref(false);
const error = ref<string | null>(null);
const currentChartType = ref('utilization');

// Chart types
const chartTypes: ChartType[] = [
    { key: 'utilization', label: 'Utilization' },
    { key: 'trips', label: 'Trips Completed' },
    { key: 'revenue', label: 'Revenue' }
];

// Generate mock trend data (in real app, fetch from API)
const generateTrendData = (type: string): ChartDataPoint[] => {
    return Array.from({ length: 30 }, (_, i) => {
        const date = format(subDays(new Date(), 29 - i), 'yyyy-MM-dd');
        let value = 0;
        
        switch (type) {
            case 'utilization':
                value = Math.floor(Math.random() * 30) + 60; // 60-90%
                break;
            case 'trips':
                value = Math.floor(Math.random() * 50) + 20; // 20-70 trips
                break;
            case 'revenue':
                value = Math.floor(Math.random() * 2000) + 1000; // $1000-3000
                break;
        }
        
        return { x: date, y: value };
    });
};

const chartData = computed(() => generateTrendData(currentChartType.value));

// Summary statistics
const summaryStats = computed<SummaryStatConfig[]>(() => {
    const data = chartData.value;
    const values = data.map(d => d.y);
    
    const total = values.reduce((sum, val) => sum + val, 0);
    const average = values.length > 0 ? total / values.length : 0;
    const peak = values.length > 0 ? Math.max(...values) : 0;
    const growth = 12.5; // Mock growth
    
    const format = currentChartType.value === 'revenue' ? 'currency' : 
                  currentChartType.value === 'utilization' ? 'percentage' : 'number';
    
    return [
        {
            key: 'total',
            label: `Total ${chartTypes.find(t => t.key === currentChartType.value)?.label}`,
            value: Math.round(total),
            format
        },
        {
            key: 'average',
            label: 'Daily Average',
            value: Math.round(average),
            format
        },
        {
            key: 'peak',
            label: 'Peak Day',
            value: Math.round(peak),
            format
        },
        {
            key: 'growth',
            label: 'Growth',
            value: growth,
            format: 'percentage'
        }
    ];
});

// Event handlers
const onChartTypeChanged = (type: string) => {
    currentChartType.value = type;
};

const onDateRangeChanged = (dates: { start: Date; end: Date }) => {
    // In real app, refetch data for new date range
    console.log('Date range changed:', dates);
    fetchTrendData();
};

const fetchTrendData = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        // Data is already computed reactively
    } catch (err) {
        error.value = 'Failed to load trend data';
    } finally {
        isLoading.value = false;
    }
};

onMounted(() => {
    fetchTrendData();
});
</script>
