<template>
  <!-- Show skeletons when loading -->
  <template v-if="status === 'pending'">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div v-for="i in 4" :key="i" class="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
        <div class="flex items-center justify-between">
          <div>
            <div class="h-3 w-20 bg-gray-200 rounded mb-2"></div>
            <div class="h-6 w-28 bg-gray-300 rounded mb-2"></div>
            <div class="h-3 w-16 bg-gray-200 rounded"></div>
          </div>
          <div class="p-3 rounded-full bg-gray-100">
            <div class="h-6 w-6 bg-gray-300 rounded-full"></div>
          </div>
        </div>
      </div>
    </div>
  </template>

  <!-- Actual metrics -->
  <template v-else>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Shuttles -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Shuttles</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalShuttles.toLocaleString() }}</p>
            <p class="text-xs text-gray-500 mt-1">
              <span :class="stats.growthVsLastMonth.totalShuttles >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ stats.growthVsLastMonth.totalShuttles >= 0 ? '+' : '' }}{{ stats.growthVsLastMonth.totalShuttles }}%
              </span>
              vs last month
            </p>
          </div>
          <div class="p-3 bg-blue-50 rounded-full">
            <Truck class="h-6 w-6 text-blue-600" />
          </div>
        </div>
      </div>

      <!-- Active Shuttles -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Active Shuttles</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.activeShuttles.toLocaleString() }}</p>
            <p class="text-xs text-gray-500 mt-1">
              <span :class="stats.growthVsLastMonth.activeShuttles >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ stats.growthVsLastMonth.activeShuttles >= 0 ? '+' : '' }}{{ stats.growthVsLastMonth.activeShuttles }}%
              </span>
              vs last month
            </p>
          </div>
          <div class="p-3 bg-green-50 rounded-full">
            <Activity class="h-6 w-6 text-green-600" />
          </div>
        </div>
      </div>

      <!-- Total Capacity -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Capacity</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalCapacity.toLocaleString() }}</p>
            <p class="text-xs text-gray-500 mt-1">
              <span :class="stats.growthVsLastMonth.totalCapacity >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ stats.growthVsLastMonth.totalCapacity >= 0 ? '+' : '' }}{{ stats.growthVsLastMonth.totalCapacity }}%
              </span>
              vs last month
            </p>
          </div>
          <div class="p-3 bg-purple-50 rounded-full">
            <Users class="h-6 w-6 text-purple-600" />
          </div>
        </div>
      </div>

      <!-- Total Trips -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Trips</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalTrips.toLocaleString() }}</p>
            <p class="text-xs text-gray-500 mt-1">
              <span :class="stats.growthVsLastMonth.totalTrips >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ stats.growthVsLastMonth.totalTrips >= 0 ? '+' : '' }}{{ stats.growthVsLastMonth.totalTrips }}%
              </span>
              vs last month
            </p>
          </div>
          <div class="p-3 bg-orange-50 rounded-full">
            <TrendingUp class="h-6 w-6 text-orange-600" />
          </div>
        </div>
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
import { Truck, Activity, Users, TrendingUp } from 'lucide-vue-next';
import type { IShuttleStats } from '~/stores/shuttle/interface/shuttle.interface';
import { useShuttleStore } from '~/stores/shuttle/shuttle.store';

const shuttleStore = useShuttleStore();

// Fetch shuttle stats using useAsyncData
const { data: stats, status, error, refresh } = await useAsyncData<IShuttleStats>('shuttle-stats', () => {
  return shuttleStore.getShuttleStats();
}, {
  default: () => ({
    totalShuttles: 0,
    activeShuttles: 0,
    totalCapacity: 0,
    totalTrips: 0,
    growthVsLastMonth: {
      totalShuttles: 0,
      activeShuttles: 0,
      totalCapacity: 0,
      totalTrips: 0
    }
  } as IShuttleStats),
  lazy: true,
});

// Refresh stats
const refreshStats = () => {
  shuttleStore.stats.data= {} as IShuttleStats; // Clear data to show loading state
  refresh();
};
</script>