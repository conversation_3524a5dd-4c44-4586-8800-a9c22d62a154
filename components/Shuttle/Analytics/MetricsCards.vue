<template>
    <AnalyticsBaseMetricsCards :metrics="shuttleMetrics" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Truck, Users, MapPin, TrendingUp, Activity, DollarSign } from 'lucide-vue-next';
import type { IShuttle } from '~/stores/shuttle/model/shuttle.model';
import type { MetricConfig } from '~/components/Analytics/BaseMetricsCards.vue';

const props = defineProps<{
    shuttles: IShuttle[];
}>();

const shuttleMetrics = computed<MetricConfig[]>(() => {
    const totalShuttles = props.shuttles.length;
    const activeShuttles = props.shuttles.filter(s => s.status?.toLowerCase() === 'active').length;
    const totalCapacity = props.shuttles.reduce((sum, shuttle) => sum + (shuttle.capacity || 0), 0);
    const averageCapacity = totalShuttles > 0 ? totalCapacity / totalShuttles : 0;
    
    // Calculate utilization rate (mock data - in real app, get from schedules/bookings)
    const utilizationRate = 78.5;
    
    // Calculate revenue per shuttle (mock data)
    const totalRevenue = 45600;
    const revenuePerShuttle = totalShuttles > 0 ? totalRevenue / totalShuttles : 0;

    return [
        {
            key: 'total_shuttles',
            label: 'Total Shuttles',
            value: totalShuttles,
            format: 'number',
            trend: 8.2,
            icon: Truck,
            iconBg: 'bg-blue-50',
            iconColor: 'text-blue-600'
        },
        {
            key: 'active_shuttles',
            label: 'Active Shuttles',
            value: activeShuttles,
            format: 'number',
            trend: 12.5,
            icon: Activity,
            iconBg: 'bg-green-50',
            iconColor: 'text-green-600'
        },
        {
            key: 'total_capacity',
            label: 'Total Capacity',
            value: totalCapacity,
            format: 'number',
            trend: 5.3,
            trendLabel: 'seats available',
            icon: Users,
            iconBg: 'bg-purple-50',
            iconColor: 'text-purple-600'
        },
        {
            key: 'utilization_rate',
            label: 'Utilization Rate',
            value: utilizationRate,
            format: 'percentage',
            trend: 3.7,
            icon: TrendingUp,
            iconBg: 'bg-orange-50',
            iconColor: 'text-orange-600'
        }
    ];
});
</script>
