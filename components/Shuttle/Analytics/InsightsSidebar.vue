<template>
    <AnalyticsBaseInsightsSidebar
        title="Shuttle Insights"
        subtitle="Real-time shuttle analytics and operations"
        :is-sheet="isSheet"
        :today-stats="todayStats"
        :alerts="alerts"
        :recent-activity="recentActivity"
        :quick-actions="quickActions"
        :performance-metrics="performanceMetrics"
        @close="$emit('close')"
        @quick-action="handleQuickAction"
    />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    AlertTriangle, 
    Info, 
    Plus, 
    Minus, 
    Settings,
    Download,
    FileText,
    Wrench
} from 'lucide-vue-next';
import type { IShuttle } from '~/stores/shuttle/model/shuttle.model';
import type {
    TodayStatConfig,
    AlertConfig,
    ActivityConfig,
    QuickActionConfig,
    PerformanceMetricConfig
} from '~/components/Analytics/BaseInsightsSidebar.vue';

const props = defineProps<{
    shuttles: IShuttle[];
    isSheet?: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
}>();

// Today's stats
const todayStats = computed<TodayStatConfig[]>(() => {
    const activeShuttles = props.shuttles.filter(s => s.status?.toLowerCase() === 'active').length;
    const maintenanceShuttles = props.shuttles.filter(s => s.status?.toLowerCase() === 'maintenance').length;
    const totalCapacity = props.shuttles.reduce((sum, shuttle) => sum + (shuttle.capacity || 0), 0);
    
    return [
        {
            key: 'active',
            label: 'Active Shuttles',
            value: activeShuttles,
            color: 'bg-green-500'
        },
        {
            key: 'maintenance',
            label: 'In Maintenance',
            value: maintenanceShuttles,
            color: 'bg-yellow-500'
        },
        {
            key: 'capacity',
            label: 'Total Capacity',
            value: totalCapacity,
            color: 'bg-blue-500'
        },
        {
            key: 'utilization',
            label: 'Avg Utilization',
            value: 78.5,
            format: 'percentage',
            color: 'bg-purple-500'
        }
    ];
});

// Alerts
const alerts = computed<AlertConfig[]>(() => {
    const maintenanceShuttles = props.shuttles.filter(s => s.status?.toLowerCase() === 'maintenance');
    const lowCapacityShuttles = props.shuttles.filter(s => (s.capacity || 0) < 10);
    
    const alertsList: AlertConfig[] = [];
    
    if (maintenanceShuttles.length > 0) {
        alertsList.push({
            id: 'maintenance',
            type: 'warning',
            title: 'Shuttles in Maintenance',
            message: `${maintenanceShuttles.length} shuttle${maintenanceShuttles.length > 1 ? 's' : ''} currently under maintenance`,
            time: '15 minutes ago',
            icon: AlertTriangle
        });
    }
    
    if (lowCapacityShuttles.length > 2) {
        alertsList.push({
            id: 'capacity',
            type: 'info',
            title: 'Low Capacity Shuttles',
            message: `${lowCapacityShuttles.length} shuttles have capacity under 10 seats`,
            time: '1 hour ago',
            icon: Info
        });
    }
    
    return alertsList;
});

// Recent activity
const recentActivity = computed<ActivityConfig[]>(() => [
    {
        id: 1,
        message: 'Shuttle SH-001 completed maintenance',
        time: '5 min ago',
        icon: Plus,
        iconColor: 'bg-green-100 text-green-600'
    },
    {
        id: 2,
        message: 'New shuttle SH-015 added to fleet',
        time: '12 min ago',
        icon: Plus,
        iconColor: 'bg-blue-100 text-blue-600'
    },
    {
        id: 3,
        message: 'Shuttle SH-008 scheduled for maintenance',
        time: '25 min ago',
        icon: Wrench,
        iconColor: 'bg-yellow-100 text-yellow-600'
    }
]);

// Quick actions
const quickActions = computed<QuickActionConfig[]>(() => [
    {
        key: 'export',
        label: 'Export Shuttle Data',
        icon: Download
    },
    {
        key: 'report',
        label: 'Generate Fleet Report',
        icon: FileText
    },
    {
        key: 'maintenance',
        label: 'Schedule Maintenance',
        icon: Wrench
    },
    {
        key: 'settings',
        label: 'Fleet Settings',
        icon: Settings
    }
]);

// Performance metrics
const performanceMetrics = computed<PerformanceMetricConfig[]>(() => [
    {
        key: 'utilization',
        label: 'Fleet Utilization',
        value: 78.5,
        format: 'percentage',
        color: 'bg-green-500',
        percentage: 78.5
    },
    {
        key: 'availability',
        label: 'Availability Rate',
        value: 92.3,
        format: 'percentage',
        color: 'bg-blue-500',
        percentage: 92.3
    },
    {
        key: 'efficiency',
        label: 'Operational Efficiency',
        value: 85.7,
        format: 'percentage',
        color: 'bg-purple-500',
        percentage: 85.7
    }
]);

// Handle quick actions
const handleQuickAction = (actionKey: string) => {
    switch (actionKey) {
        case 'export':
            // Handle export
            console.log('Exporting shuttle data...');
            break;
        case 'report':
            // Handle report generation
            console.log('Generating fleet report...');
            break;
        case 'maintenance':
            // Handle maintenance scheduling
            console.log('Opening maintenance scheduler...');
            break;
        case 'settings':
            // Handle settings
            console.log('Opening fleet settings...');
            break;
    }
};
</script>
