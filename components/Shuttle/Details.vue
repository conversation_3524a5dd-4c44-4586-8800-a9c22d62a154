<template>
    <div class="  bg-white rounded-lg p-5">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="font-bold text-2xl">Shuttle Details</h1>
                <div class="mt-5">
                    <p class="text-muted-foreground text-xs">ID: <span>{{ getShuttleId(shuttle) }}</span></p>
                </div>
            </div>

            <ShuttleMoreOptions :shuttle="shuttle"></ShuttleMoreOptions>
        </div>
        <div class="flex items-center gap-5 mt-5">
            <div class="avatar">
                <div class="w-24 rounded">
                    <img
                        src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRkDxw6l8pxYeVttcBO0hNJ8esvVfQJ-GF-C3sOHOoz0RQ0t2dq_TIC4yKihmaq0bIPb-o&usqp=CAU" />
                </div>
            </div>
            <div>
                <h1 class="text-2xl">{{ shuttle.make }} {{ shuttle.model
                    }}</h1>
                <div class="flex gap-2 mt-2">
                    <p>{{ shuttle.plate_number }}</p>
                    <Badge class="bg-gray-400 text-black" :class="{ 'bg-green-400': getStatusName(shuttle) === 'Active' }">
                        {{ getStatusName(shuttle) }}</Badge>
                </div>



            </div>
            <div class="flex-grow"></div>
            <div v-if="hasDriver(shuttle)" class="flex flex-col gap- justify-end text-right">
                <p class="text-muted-foreground text-xs">Current Driver</p>
                <div class="flex gap-2 items-center" >
                <Avatar size="sm" style="width:30px; height: 30px">
                    <AvatarImage :src="getDriverImage(shuttle)" alt="Driver image" />
                    <AvatarFallback>DD</AvatarFallback>
                </Avatar>
                <p>{{ getDriverName(shuttle) }}</p>
            </div>
            </div>

            <template v-else>
                <ShuttleAssignDriverButton :shuttle="shuttle"></ShuttleAssignDriverButton>
            </template>

        </div>

        <div class="mt-5 flex justify-between">
            <div>
                <p class="mt-1 text-xs font-medium text-gray-500"><span>
                        <Icon size="18" name="ic:round-event-seat"></Icon>
                    </span> Capacity<span class="text-primary"> </span></p>
                <h1>{{ getCapacity(shuttle) }} Seats</h1>
            </div>
            <div>
                <p class="mt-1 text-xs font-medium text-gray-500"><span>

                    </span> Owner<span class="text-primary"> </span></p>
                <div class="flex gap-2 items-center">
                    <Avatar size="sm" style="width:25px; height: 25px">
                        <AvatarImage :src="getOwnerLogo()" alt="Owner image" />
                        <AvatarFallback>DD</AvatarFallback>
                    </Avatar>
                    <p>{{ getOwnerName() }}</p>
                </div>
            </div>
            <div>
                <p class="mt-1 text-xs font-medium text-gray-500"><span>
                        <Icon size="18" name="material-symbols:route"></Icon>
                    </span> Total Routes<span class="text-primary"> </span></p>
                <h1>{{ routesCount }}</h1>
            </div>
            <div>
                <p class="mt-1 text-xs font-medium text-gray-500"><span>
                    </span> Class<span class="text-primary"> </span></p>
                <h1>{{ getClassName(shuttle) }}</h1>
            </div>
            <div>
                <p class="mt-1 text-xs font-medium text-gray-500"><span>
                        <Icon size="18" name="material-symbols:check-circle-outline"></Icon>
                    </span>Completed Trips<span class="text-primary"> </span></p>
                <h1>{{ completedRoutesCount }}</h1>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { type IShuttle } from '~/stores/shuttle/model/shuttle.model';
import { usePartnerStore } from '~/stores/partner/partner.store';
import { useRouteStore } from '~/stores/route/route.store';
import { ref, onMounted } from 'vue';

// Helper functions to handle different data structures
const getStatusName = (shuttle: any): string => {
    if (shuttle.status?.name) {
        return shuttle.status.name;
    } else if (typeof shuttle.status === 'string') {
        return shuttle.status === 'active' ? 'Active' : shuttle.status;
    }
    return 'Unknown';
};

const getCapacity = (shuttle: any): number => {
    return shuttle.number_of_seats || shuttle.capacity || 0;
};

const getClassName = (shuttle: any): string => {
    if (shuttle.class?.name) {
        return shuttle.class.name;
    }
    return 'Standard';
};

const getShuttleId = (shuttle: any): string => {
    return shuttle.objectId || shuttle.id || '';
};

const hasDriver = (shuttle: any): boolean => {
    return !!shuttle.driver;
};

const getDriverImage = (shuttle: any): string => {
    return shuttle.driver?.imageUrl || '';
};

const getDriverName = (shuttle: any): string => {
    if (shuttle.driver) {
        return `${shuttle.driver.firstname || ''} ${shuttle.driver.lastname || ''}`;
    }
    return '';
};

const props = defineProps<{ shuttle: IShuttle }>();
const partnerStore = usePartnerStore();
const routeStore = useRouteStore();

// Create reactive data
const owner = ref<{ name: string; logo: string; id: string }>({
    name: '',
    logo: '',
    id: ''
});

// Reactive variables for routes counts
const routesCount = ref(0);
const completedRoutesCount = ref(0);

// Function to get owner details
const getOwner = async (shuttle: IShuttle) => {
    if (!shuttle.partner_id) return;

    try {
        // Try to find the partner in the existing partners list first
        const existingPartner = partnerStore.partners.find(p => p.id === shuttle.partner_id);

        if (existingPartner) {
            owner.value = {
                name: existingPartner.name,
                logo: existingPartner.avatar_url,
                id: existingPartner.id
            };
            return;
        }

        // If not found, fetch from API
        const partners = await partnerStore.getPartners(shuttle.partner_id);
        if (partners && partners.length > 0) {
            const partner = partners[0];
            owner.value = {
                name: partner.name,
                logo: partner.avatar_url,
                id: partner.id
            };
        }
    } catch (error) {
        console.error('Error fetching owner details:', error);
    }
};

// Function to get routes count
const getRoutesCount = async (shuttle: IShuttle) => {
    if (!shuttle.id) return;

    try {
        const count = await routeStore.getShuttleRoutesCount(shuttle.id);
        routesCount.value = count;
    } catch (error) {
        console.error('Error fetching routes count:', error);
        routesCount.value = 0;
    }
};

// Function to get completed routes count
const getCompletedRoutesCount = async (shuttle: IShuttle) => {
    if (!shuttle.id) return;

    try {
        const count = await routeStore.getShuttleCompletedRoutesCount(shuttle.id);
        completedRoutesCount.value = count;
    } catch (error) {
        console.error('Error fetching completed routes count:', error);
        completedRoutesCount.value = 0;
    }
};

// Load data when component mounts
onMounted(() => {
    getOwner(props.shuttle);
    getRoutesCount(props.shuttle);
    getCompletedRoutesCount(props.shuttle);
});

const getOwnerLogo = (): string => {
    return owner.value.logo || '';
};

const getOwnerName = (): string => {
    return owner.value.name || props.shuttle.partner_id || '';
};
</script>