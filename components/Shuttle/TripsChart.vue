<template>
    <div class="w-full max-h-[340px] h-full bg-white rounded-xl p-5 flex flex-col gap-2">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="font-bold text-lg">{{ 'Completed Trips Trend' }}</h1>
                <p class="text-xs text-muted-foreground">{{ 'Number of trips completed by shuttle' }}</p>
            </div>
            <DateRangePicker v-if="!isLoading" @handle-date-change="onDateChanged"></DateRangePicker>
        </div>
        <div v-if="isLoading" class="w-full h-full flex items-center justify-center">
            <Loader2 class="h-8 w-8 animate-spin text-primary" />
        </div>
        <div v-else-if="error" class="w-full h-full flex items-center justify-center">
            <div class="text-center">
                <p class="text-red-500">{{ error }}</p>
                <Button variant="outline" size="sm" class="mt-2" @click="fetchTrendData">Retry</Button>
            </div>
        </div>
        <template v-else>
            <ClientOnly>
                <apexchart :key="chartOptions.series" height="100%" width="100%" :options="chartOptions"
                    :series="chartOptions.series">
                </apexchart>
            </ClientOnly>
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { Loader2 } from 'lucide-vue-next';
import { useScheduleStore } from '~/stores/schedule/schedule.store';
import type { TrendData } from '~/stores/schedule/schedule.store';
import { format, subDays } from 'date-fns';

const props = defineProps<{
    shuttleId: string;
}>();

const scheduleStore = useScheduleStore();
const isLoading = ref(false);
const error = ref<string | null>(null);
const trendData = ref<TrendData | null>(null);

// Date range for filtering
const startDate = ref(format(subDays(new Date(), 30), 'yyyy-MM-dd'));
const endDate = ref(format(new Date(), 'yyyy-MM-dd'));

// Fetch trend data
const fetchTrendData = async () => {
    isLoading.value = true;
    error.value = null;

    try {
        const data = await scheduleStore.getCompletedTripsTrend(
            props.shuttleId,
            startDate.value,
            endDate.value,
            'day'
        );

        if (scheduleStore.failedCompletedTripsTrend) {
            error.value = scheduleStore.failureCompletedTripsTrend.message;
        } else if (data) {
            trendData.value = data;
        } else {
            error.value = 'No data available';
        }
    } catch (err) {
        console.error('Error fetching trend data:', err);
        error.value = 'Failed to load trend data';
    } finally {
        isLoading.value = false;
    }
};

// Handle date range changes
const onDateChanged = (dates: { start: Date; end: Date }) => {
    startDate.value = format(dates.start, 'yyyy-MM-dd');
    endDate.value = format(dates.end, 'yyyy-MM-dd');
    fetchTrendData();
};

// Process chart data
const chartData = computed(() => {
    if (!trendData.value) return [];

    return trendData.value.trend.map(item => ({
        x: item.date,
        y: item.count
    }));
});

// Chart options
const chartOptions = computed(() => ({
    chart: {
        type: 'area',
        height: 250,
        toolbar: {
            show: false,
        },
        zoom: {
            enabled: false,
        },
    },
    series: [{
        name: 'Completed Trips',
        data: chartData.value
    }],
    dataLabels: {
        enabled: false,
    },
    stroke: {
        show: true,
        curve: 'smooth',
        lineCap: 'butt',
        colors: undefined,
        width: 2,
    },
    xaxis: {
        type: 'datetime',
        labels: {
            format: 'dd MMM',
        }
    },
    yaxis: {
        show: true,
        title: {
            text: 'Trips',
        },
        min: 0,
        forceNiceScale: true,
    },
    tooltip: {
        enabled: true,
        x: {
            format: 'dd MMM yyyy'
        }
    },
    fill: {
        opacity: 0.5,
    },
    colors: ['#00E396'],
    grid: {
        show: true,
        yaxis: {
            lines: {
                show: true,
            }
        }
    },
    legend: {
        show: true,
        position: 'top',
        horizontalAlign: 'center',
    },
}));

// Watch for changes in shuttleId
watch(() => props.shuttleId, () => {
    if (props.shuttleId) {
        fetchTrendData();
    }
}, { immediate: true });

// Load data on component mount
onMounted(() => {
    if (props.shuttleId) {
        fetchTrendData();
    }
});

</script>