<template>

    <section class="p-2 flex flex-col gap-2">
        <ShuttleDetails :shuttle="shuttle"></ShuttleDetails>
        <div class="overflow-hidden bg-white border border-gray-200 rounded-xl col-span-12">
            <ShuttleTripsChart :shuttleId="shuttle.id"></ShuttleTripsChart>
        </div>
        <!-- <ShuttleRoute :shuttle="shuttle"></ShuttleRoute> -->
        <ShuttleSchedules :shuttle="shuttle"></ShuttleSchedules>
        <!-- <ShuttleBookingHistory :shuttle="shuttle"></ShuttleBookingHistory> -->


    </section>


</template>
<script setup lang="ts">
import { type IShuttle } from '~/stores/shuttle/model/shuttle.model';


const props = defineProps<{ shuttle: IShuttle }>();

</script>