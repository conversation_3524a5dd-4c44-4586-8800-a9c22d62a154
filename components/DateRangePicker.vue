<template>
    <div>
        <Popover>
            <PopoverTrigger as-child>
                <Button variant="outline" size="sm" class="h-8 border-dashed">
                    <CalendarIcon class="mr-2 h-4 w-4" />
                    {{ formattedDateRange }}
                    <ChevronDown class="ml-2 h-4 w-4 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent class="w-auto p-0" align="end">
                <Calendar
                    v-model:value="date"
                    mode="range"
                    :disabled-date="disabledDate"
                    class="rounded-md border"
                />
                <div class="p-3 border-t border-border">
                    <div class="flex items-center justify-between">
                        <Button variant="outline" size="sm" @click="resetDateRange">Reset</Button>
                        <Button size="sm" @click="applyDateRange">Apply</Button>
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { format, isAfter, isBefore, subDays } from 'date-fns';
import { CalendarIcon, ChevronDown } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

const emit = defineEmits(['handle-date-change']);

// Default date range: last 30 days
const defaultStartDate = subDays(new Date(), 30);
const defaultEndDate = new Date();

// Date state
const date = ref<Date[] | undefined>([defaultStartDate, defaultEndDate]);

// Format the date range for display
const formattedDateRange = computed(() => {
    if (!date.value || date.value.length === 0) {
        return 'Select date range';
    }
    
    const start = date.value[0];
    const end = date.value[1] || date.value[0];
    
    if (!start) {
        return 'Select date range';
    }
    
    if (!end || start === end) {
        return format(start, 'MMM dd, yyyy');
    }
    
    return `${format(start, 'MMM dd, yyyy')} - ${format(end, 'MMM dd, yyyy')}`;
});

// Disable dates in the future
const disabledDate = (current: Date) => {
    return isAfter(current, new Date());
};

// Reset date range to default
const resetDateRange = () => {
    date.value = [defaultStartDate, defaultEndDate];
};

// Apply the selected date range
const applyDateRange = () => {
    if (date.value && date.value.length >= 1) {
        const start = date.value[0];
        const end = date.value[1] || date.value[0];
        
        if (start) {
            emit('handle-date-change', { start, end });
        }
    }
};

// Watch for date changes
watch(date, (newValue) => {
    if (newValue && newValue.length === 2 && newValue[0] && newValue[1]) {
        // Auto-apply when both dates are selected
        applyDateRange();
    }
}, { deep: true });
</script>
