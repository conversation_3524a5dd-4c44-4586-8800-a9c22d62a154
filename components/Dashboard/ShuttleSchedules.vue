<template>
  <div class="p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Today's Schedules</h3>
      <NuxtLink
        to="/dashboard/routes"
        class="text-sm text-blue-600 hover:text-blue-800"
      >
        View All →
      </NuxtLink>
    </div>

    <div v-if="isLoading" class="space-y-3">
      <div v-for="i in 4" :key="i" class="animate-pulse">
        <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
          <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div class="w-16 h-6 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>

    <div v-else-if="schedules.length === 0" class="text-center py-8">
      <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <Clock class="w-8 h-8 text-gray-400" />
      </div>
      <h3 class="text-sm font-medium text-gray-900 mb-1">No schedules today</h3>
      <p class="text-sm text-gray-500">Today's shuttle schedules will appear here.</p>
    </div>

    <div v-else class="space-y-3">
      <div
        v-for="schedule in schedules.slice(0, 4)"
        :key="schedule.id"
        class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
        @click="viewScheduleDetails(schedule)"
      >
        <!-- Time Badge -->
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex flex-col items-center justify-center">
            <span class="text-xs font-semibold text-blue-600">
              {{ formatTime(schedule.departure_time) }}
            </span>
            <span class="text-xs text-blue-500">
              {{ formatAMPM(schedule.departure_time) }}
            </span>
          </div>
        </div>

        <!-- Route Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2 mb-1">
            <p class="text-sm font-medium text-gray-900 truncate">
              {{ schedule.route?.origin }} → {{ schedule.route?.destination }}
            </p>
            <span
              class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
              :class="{
                'bg-green-100 text-green-800': schedule.status === 'active',
                'bg-yellow-100 text-yellow-800': schedule.status === 'scheduled',
                'bg-red-100 text-red-800': schedule.status === 'cancelled',
                'bg-blue-100 text-blue-800': schedule.status === 'completed',
                'bg-gray-100 text-gray-800': !['active', 'scheduled', 'cancelled', 'completed'].includes(schedule.status)
              }"
            >
              {{ schedule.status }}
            </span>
          </div>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-1">
              <Truck class="w-3 h-3 text-gray-400" />
              <span class="text-xs text-gray-500">
                {{ schedule.shuttle?.name || 'N/A' }}
              </span>
            </div>
            <div class="flex items-center space-x-1">
              <Users class="w-3 h-3 text-gray-400" />
              <span class="text-xs text-gray-500">
                {{ schedule.booked_seats || 0 }}/{{ schedule.shuttle?.capacity || 0 }}
              </span>
            </div>
          </div>
        </div>

        <!-- Status & Price -->
        <div class="flex-shrink-0 text-right">
          <p class="text-sm font-semibold text-gray-900">
            ₦{{ formatAmount(schedule.route?.base_price || 0) }}
          </p>
          <div class="flex items-center justify-end space-x-1 mt-1">
            <div
              class="w-2 h-2 rounded-full"
              :class="{
                'bg-green-500': schedule.status === 'active',
                'bg-yellow-500': schedule.status === 'scheduled',
                'bg-red-500': schedule.status === 'cancelled',
                'bg-blue-500': schedule.status === 'completed',
                'bg-gray-400': !['active', 'scheduled', 'cancelled', 'completed'].includes(schedule.status)
              }"
            ></div>
            <span class="text-xs text-gray-500">
              {{ getStatusText(schedule.status) }}
            </span>
          </div>
        </div>

        <!-- Arrow -->
        <div class="flex-shrink-0">
          <ChevronRight class="w-4 h-4 text-gray-400" />
        </div>
      </div>
    </div>

    <!-- Summary Stats -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <div class="grid grid-cols-3 gap-4">
        <div class="text-center">
          <p class="text-lg font-bold text-green-600">{{ activeSchedules }}</p>
          <p class="text-xs text-gray-500">Active</p>
        </div>
        <div class="text-center">
          <p class="text-lg font-bold text-yellow-600">{{ scheduledCount }}</p>
          <p class="text-xs text-gray-500">Scheduled</p>
        </div>
        <div class="text-center">
          <p class="text-lg font-bold text-blue-600">{{ completedCount }}</p>
          <p class="text-xs text-gray-500">Completed</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Clock, Truck, Users, ChevronRight } from 'lucide-vue-next'

interface Schedule {
  id: string
  departure_time: string
  status: string
  booked_seats?: number
  route?: {
    origin: string
    destination: string
    base_price: number
  }
  shuttle?: {
    name: string
    capacity: number
  }
}

interface Props {
  schedules: Schedule[]
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

const activeSchedules = computed(() => 
  props.schedules.filter(s => s.status === 'active').length
)

const scheduledCount = computed(() => 
  props.schedules.filter(s => s.status === 'scheduled').length
)

const completedCount = computed(() => 
  props.schedules.filter(s => s.status === 'completed').length
)

const formatTime = (timeString: string) => {
  if (!timeString) return '--'
  try {
    const date = new Date(timeString)
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  } catch {
    return '--'
  }
}

const formatAMPM = (timeString: string) => {
  if (!timeString) return ''
  try {
    const date = new Date(timeString)
    return date.toLocaleTimeString('en-US', { 
      hour12: true 
    }).split(' ')[1]
  } catch {
    return ''
  }
}

const formatAmount = (amount: number) => {
  if (!amount) return '0.00'
  return amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return 'Running'
    case 'scheduled': return 'Pending'
    case 'cancelled': return 'Cancelled'
    case 'completed': return 'Done'
    default: return status
  }
}

const viewScheduleDetails = (schedule: Schedule) => {
  navigateTo(`/dashboard/routes?schedule=${schedule.id}`)
}
</script>
