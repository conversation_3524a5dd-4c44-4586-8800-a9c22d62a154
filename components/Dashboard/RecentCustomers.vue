<template>
  <div class="p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Recent Customers</h3>
      <NuxtLink
        to="/dashboard/customers"
        class="text-sm text-blue-600 hover:text-blue-800"
      >
        View All →
      </NuxtLink>
    </div>

    <div v-if="isLoading" class="space-y-3">
      <div v-for="i in 4" :key="i" class="animate-pulse">
        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <div class="w-10 h-10 bg-gray-200 rounded-full"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="customers.length === 0" class="text-center py-8">
      <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <Users class="w-8 h-8 text-gray-400" />
      </div>
      <h3 class="text-sm font-medium text-gray-900 mb-1">No customers yet</h3>
      <p class="text-sm text-gray-500">Customer activity will appear here.</p>
    </div>

    <div v-else class="space-y-3">
      <div
        v-for="customer in customers.slice(0, 4)"
        :key="customer.id"
        class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
        @click="viewCustomerDetails(customer)"
      >
        <!-- Avatar -->
        <div class="flex-shrink-0">
          <img
            v-if="customer.avatar_url"
            :src="customer.avatar_url"
            :alt="customer.first_name"
            class="w-10 h-10 rounded-full object-cover"
          />
          <div
            v-else
            class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"
          >
            <span class="text-sm font-medium text-blue-600">
              {{ getInitials(customer.first_name, customer.last_name) }}
            </span>
          </div>
        </div>

        <!-- Customer Info -->
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 truncate">
            {{ customer.first_name }} {{ customer.last_name }}
          </p>
          <div class="flex items-center space-x-2 mt-1">
            <p class="text-xs text-gray-500 truncate">
              {{ customer.email }}
            </p>
            <span class="text-xs text-gray-400">•</span>
            <p class="text-xs text-gray-500">
              {{ customer.phone_number }}
            </p>
          </div>
        </div>

        <!-- Status/Activity -->
        <div class="flex-shrink-0 text-right">
          <div class="flex items-center space-x-1">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span class="text-xs text-gray-500">Active</span>
          </div>
          <p class="text-xs text-gray-400 mt-1">
            {{ formatJoinDate(customer.created_at) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Summary Stats -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center">
          <p class="text-2xl font-bold text-gray-900">{{ totalCustomers }}</p>
          <p class="text-xs text-gray-500">Total Customers</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-green-600">{{ newThisMonth }}</p>
          <p class="text-xs text-gray-500">New This Month</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Users } from 'lucide-vue-next'

interface Customer {
  id: string
  first_name: string
  last_name: string
  email: string
  phone_number: string
  avatar_url?: string
  created_at: string
}

interface Props {
  customers: Customer[]
  isLoading?: boolean
  totalCustomers?: number
  newThisMonth?: number
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  totalCustomers: 0,
  newThisMonth: 0
})

const getInitials = (firstName?: string, lastName?: string) => {
  if (!firstName && !lastName) return 'U'
  return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase()
}

const formatJoinDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return 'Today'
    if (diffDays === 2) return 'Yesterday'
    if (diffDays <= 7) return `${diffDays} days ago`
    if (diffDays <= 30) return `${Math.ceil(diffDays / 7)} weeks ago`
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
  } catch {
    return 'N/A'
  }
}

const viewCustomerDetails = (customer: Customer) => {
  navigateTo(`/dashboard/customers?customer=${customer.id}`)
}
</script>
