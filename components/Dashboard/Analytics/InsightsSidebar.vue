<template>
    <AnalyticsBaseInsightsSidebar
        title="Business Insights"
        subtitle="Real-time business analytics and key insights"
        :is-sheet="isSheet"
        :today-stats="todayStats"
        :alerts="alerts"
        :recent-activity="recentActivity"
        :quick-actions="quickActions"
        :performance-metrics="performanceMetrics"
        @close="$emit('close')"
        @quick-action="handleQuickAction"
    />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    AlertTriangle, 
    Info, 
    Plus, 
    Minus, 
    Settings,
    Download,
    FileText,
    BarChart3,
    Users,
    Calendar,
    DollarSign,
    TrendingUp
} from 'lucide-vue-next';
import type {
    TodayStatConfig,
    AlertConfig,
    ActivityConfig,
    QuickActionConfig,
    PerformanceMetricConfig
} from '~/components/Analytics/BaseInsightsSidebar.vue';

const props = defineProps<{
    bookingCount?: { count: number; total: number } | null;
    transactionsSum?: { count: number; total: number } | null;
    routeCount?: { count: number; total: number } | null;
    shuttleCount?: { count: number; total: number } | null;
    todayBookings?: { count: number; total: number } | null;
    availableDrivers?: { count: number; total: number } | null;
    isSheet?: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
}>();

// Today's stats
const todayStats = computed<TodayStatConfig[]>(() => {
    const todayBookingsCount = props.todayBookings?.count || 0;
    const todayRevenue = Math.floor((props.transactionsSum?.total || 0) * 0.1); // Mock today's portion
    const activeShuttles = props.shuttleCount?.count || 0;
    const availableDriversCount = props.availableDrivers?.count || 0;
    
    return [
        {
            key: 'today_bookings',
            label: "Today's Bookings",
            value: todayBookingsCount,
            color: 'bg-blue-500'
        },
        {
            key: 'today_revenue',
            label: "Today's Revenue",
            value: todayRevenue,
            format: 'currency',
            color: 'bg-green-500'
        },
        {
            key: 'active_shuttles',
            label: 'Active Shuttles',
            value: activeShuttles,
            color: 'bg-purple-500'
        },
        {
            key: 'available_drivers',
            label: 'Available Drivers',
            value: availableDriversCount,
            color: 'bg-orange-500'
        }
    ];
});

// Alerts
const alerts = computed<AlertConfig[]>(() => {
    const alertsList: AlertConfig[] = [];
    
    const completionRate = props.bookingCount?.total ? 
        (props.bookingCount.count / props.bookingCount.total) * 100 : 0;
    
    if (completionRate < 80) {
        alertsList.push({
            id: 'completion_rate',
            type: 'warning',
            title: 'Low Completion Rate',
            message: `Booking completion rate is ${completionRate.toFixed(1)}% - below target of 80%`,
            time: '30 minutes ago',
            icon: AlertTriangle
        });
    }
    
    const fleetUtilization = props.shuttleCount?.total ? 
        (props.shuttleCount.count / props.shuttleCount.total) * 100 : 0;
    
    if (fleetUtilization > 90) {
        alertsList.push({
            id: 'fleet_capacity',
            type: 'info',
            title: 'High Fleet Utilization',
            message: `${fleetUtilization.toFixed(1)}% of fleet is active - consider expanding capacity`,
            time: '1 hour ago',
            icon: Info
        });
    }
    
    return alertsList;
});

// Recent activity
const recentActivity = computed<ActivityConfig[]>(() => [
    {
        id: 1,
        message: 'New booking confirmed for Route A',
        time: '2 min ago',
        icon: Plus,
        iconColor: 'bg-green-100 text-green-600'
    },
    {
        id: 2,
        message: 'Driver John completed trip to Airport',
        time: '8 min ago',
        icon: Users,
        iconColor: 'bg-blue-100 text-blue-600'
    },
    {
        id: 3,
        message: 'New route "City Express" activated',
        time: '15 min ago',
        icon: Plus,
        iconColor: 'bg-purple-100 text-purple-600'
    },
    {
        id: 4,
        message: 'Payment processed: $245.50',
        time: '22 min ago',
        icon: DollarSign,
        iconColor: 'bg-green-100 text-green-600'
    },
    {
        id: 5,
        message: 'Shuttle SH-003 completed maintenance',
        time: '35 min ago',
        icon: Settings,
        iconColor: 'bg-yellow-100 text-yellow-600'
    }
]);

// Quick actions
const quickActions = computed<QuickActionConfig[]>(() => [
    {
        key: 'export_dashboard',
        label: 'Export Dashboard Data',
        icon: Download
    },
    {
        key: 'business_report',
        label: 'Generate Business Report',
        icon: FileText
    },
    {
        key: 'analytics',
        label: 'Advanced Analytics',
        icon: BarChart3
    },
    {
        key: 'system_settings',
        label: 'System Settings',
        icon: Settings
    }
]);

// Performance metrics
const performanceMetrics = computed<PerformanceMetricConfig[]>(() => {
    const completionRate = props.bookingCount?.total ? 
        (props.bookingCount.count / props.bookingCount.total) * 100 : 0;
    
    const fleetUtilization = props.shuttleCount?.total ? 
        (props.shuttleCount.count / props.shuttleCount.total) * 100 : 0;
    
    const routeEfficiency = props.routeCount?.total ? 
        (props.routeCount.count / props.routeCount.total) * 100 : 0;
    
    return [
        {
            key: 'completion_rate',
            label: 'Booking Completion Rate',
            value: completionRate,
            format: 'percentage',
            color: completionRate >= 80 ? 'bg-green-500' : 'bg-red-500',
            percentage: completionRate
        },
        {
            key: 'fleet_utilization',
            label: 'Fleet Utilization',
            value: fleetUtilization,
            format: 'percentage',
            color: 'bg-blue-500',
            percentage: fleetUtilization
        },
        {
            key: 'route_efficiency',
            label: 'Route Efficiency',
            value: routeEfficiency,
            format: 'percentage',
            color: 'bg-purple-500',
            percentage: routeEfficiency
        },
        {
            key: 'revenue_growth',
            label: 'Revenue Growth',
            value: 15.3,
            format: 'percentage',
            color: 'bg-green-500',
            percentage: 76.5 // 15.3% out of 20% target
        }
    ];
});

// Handle quick actions
const handleQuickAction = (actionKey: string) => {
    switch (actionKey) {
        case 'export_dashboard':
            console.log('Exporting dashboard data...');
            break;
        case 'business_report':
            console.log('Generating business report...');
            break;
        case 'analytics':
            console.log('Opening advanced analytics...');
            break;
        case 'system_settings':
            console.log('Opening system settings...');
            break;
    }
};
</script>
