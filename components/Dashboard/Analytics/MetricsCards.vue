<template>
    <AnalyticsBaseMetricsCards :metrics="dashboardMetrics" :loading="true" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    Calendar, 
    DollarSign, 
    MapPin, 
    CheckCircle
} from 'lucide-vue-next';
import type { MetricConfig } from '~/components/Analytics/BaseMetricsCards.vue';

const props = defineProps<{
    bookingCount?: { count: number; total: number } | null;
    transactionsSum?: { count: number; total: number } | null;
    routeCount?: { count: number; total: number } | null;
    todayBookings?: { count: number; total: number } | null;
}>();

const dashboardMetrics = computed<MetricConfig[]>(() => {
    const completedBookings = props.bookingCount?.count || 0;
    const totalEarnings = props.transactionsSum?.total || 0;
    const activeRoutes = props.routeCount?.count || 0;
    const todayBookingsCount = props.todayBookings?.count || 0;

    

    return [
        {
            key: 'total_revenue',
            label: 'Total Revenue',
            value: totalEarnings,
            format: 'currency',
            trend: 15.3,
            icon: DollarSign,
            iconBg: 'bg-green-50',
            iconColor: 'text-green-600'
        },
        {
            key: 'completed_bookings',
            label: 'Completed Bookings',
            value: completedBookings,
            format: 'number',
            trend: 8.7,
            icon: CheckCircle,
            iconBg: 'bg-blue-50',
            iconColor: 'text-blue-600'
        },
        {
            key: 'active_routes',
            label: 'Active Routes',
            value: activeRoutes,
            format: 'number',
            trend: 5.2,
            icon: MapPin,
            iconBg: 'bg-purple-50',
            iconColor: 'text-purple-600'
        },
        {
            key: 'today_bookings',
            label: "Today's Bookings",
            value: todayBookingsCount,
            format: 'number',
            trend: 12.4,
            trendLabel: 'vs yesterday',
            icon: Calendar,
            iconBg: 'bg-indigo-50',
            iconColor: 'text-indigo-600'
        },
     
    ];
});
</script>
