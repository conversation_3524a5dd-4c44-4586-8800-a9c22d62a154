<template>
    <AnalyticsBaseMetricsCards :metrics="dashboardMetrics" :loading="loading" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
    Calendar,
    DollarSign,
    MapPin,
    CheckCircle
} from 'lucide-vue-next';
import type { MetricConfig } from '~/components/Analytics/BaseMetricsCards.vue';

interface OrganizationStats {
    totalRevenue: number;
    completedBookings: number;
    activeRoutes: number;
    todayBookings: number;
}

const props = defineProps<{
    stats?: OrganizationStats | null;
    loading?: boolean;
}>();

const dashboardMetrics = computed<MetricConfig[]>(() => {
    const stats = props.stats;

    if (!stats) {
        return [
            {
                key: 'total_revenue',
                label: 'Total Revenue',
                value: 0,
                format: 'currency',
                icon: DollarSign,
                iconBg: 'bg-green-50',
                iconColor: 'text-green-600'
            },
            {
                key: 'completed_bookings',
                label: 'Completed Bookings',
                value: 0,
                format: 'number',
                icon: CheckCircle,
                iconBg: 'bg-blue-50',
                iconColor: 'text-blue-600'
            },
            {
                key: 'active_routes',
                label: 'Active Routes',
                value: 0,
                format: 'number',
                icon: MapPin,
                iconBg: 'bg-purple-50',
                iconColor: 'text-purple-600'
            },
            {
                key: 'today_bookings',
                label: "Today's Bookings",
                value: 0,
                format: 'number',
                trendLabel: 'vs yesterday',
                icon: Calendar,
                iconBg: 'bg-indigo-50',
                iconColor: 'text-indigo-600'
            }
        ];
    }

    return [
        {
            key: 'total_revenue',
            label: 'Total Revenue',
            value: stats.totalRevenue,
            format: 'currency',
            trend: 15.3, // TODO: Get trend from API when available
            icon: DollarSign,
            iconBg: 'bg-green-50',
            iconColor: 'text-green-600'
        },
        {
            key: 'completed_bookings',
            label: 'Completed Bookings',
            value: stats.completedBookings,
            format: 'number',
            trend: 8.7, // TODO: Get trend from API when available
            icon: CheckCircle,
            iconBg: 'bg-blue-50',
            iconColor: 'text-blue-600'
        },
        {
            key: 'active_routes',
            label: 'Active Routes',
            value: stats.activeRoutes,
            format: 'number',
            trend: 5.2, // TODO: Get trend from API when available
            icon: MapPin,
            iconBg: 'bg-purple-50',
            iconColor: 'text-purple-600'
        },
        {
            key: 'today_bookings',
            label: "Today's Bookings",
            value: stats.todayBookings,
            format: 'number',
            trend: 12.4, // TODO: Get trend from API when available
            trendLabel: 'vs yesterday',
            icon: Calendar,
            iconBg: 'bg-indigo-50',
            iconColor: 'text-indigo-600'
        }
    ];
});
</script>
