<template>
    <AnalyticsBaseTrendsChart
        title="Business Performance Trends"
        subtitle="Track key business metrics and performance over time"
        :chart-types="chartTypes"
        :data="chartData"
        :is-loading="computedIsLoading"
        :error="computedError"
        :summary-stats="summaryStats"
        :colors="['#3B82F6', '#10B981', '#F59E0B', '#EF4444']"
        @chart-type-changed="onChartTypeChanged"
        @date-range-changed="onDateRangeChanged"
        @retry="handleRetry"
    />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import type { ChartType, SummaryStatConfig, ChartDataPoint } from '~/components/Analytics/BaseTrendsChart.vue';
import { useOrganizationStore } from '~/stores/organization/organization.store';

const organizationStore = useOrganizationStore();
const currentChartType = ref('revenue');

// Reactive data fetching based on chart type
const { data: revenueData, status: revenueStatus, error: revenueError, refresh: refreshRevenue } = useAsyncData(
    'dashboard-revenue-trends',
    () => organizationStore.getEarningsTrend(),
    { lazy: true, server: false }
);

const { data: bookingsData, status: bookingsStatus, error: bookingsError, refresh: refreshBookings } = useAsyncData(
    'dashboard-bookings-trends',
    () => organizationStore.getBookingsTrend(),
    { lazy: true, server: false }
);

// Chart types
const chartTypes: ChartType[] = [
    { key: 'revenue', label: 'Revenue' },
    { key: 'bookings', label: 'Bookings' },
];

// Transform API data to chart format
const transformApiData = (data: any[]): ChartDataPoint[] => {
    if (!data || !Array.isArray(data)) return [];

    return data.map((item: any) => ({
        x: item.date || item.x || new Date().toISOString().split('T')[0],
        y: item.value || item.y || item.amount || item.count || 0
    }));
};

// Computed chart data based on current type
const chartData = computed(() => {
    if (currentChartType.value === 'revenue') {
        return transformApiData(revenueData.value || []);
    } else if (currentChartType.value === 'bookings') {
        return transformApiData(bookingsData.value || []);
    }
    return [];
});

// Computed loading state
const computedIsLoading = computed(() => {
    if (currentChartType.value === 'revenue') {
        return revenueStatus.value === 'pending';
    } else if (currentChartType.value === 'bookings') {
        return bookingsStatus.value === 'pending';
    }
    return false;
});

// Computed error state
const computedError = computed(() => {
    if (currentChartType.value === 'revenue') {
        return revenueError.value?.message || null;
    } else if (currentChartType.value === 'bookings') {
        return bookingsError.value?.message || null;
    }
    return null;
});

// Summary statistics
const summaryStats = computed<SummaryStatConfig[]>(() => {
    const data = chartData.value;
    const values = data.map(d => d.y);
    
    const total = values.reduce((sum, val) => sum + val, 0);
    const average = values.length > 0 ? total / values.length : 0;
    const peak = values.length > 0 ? Math.max(...values) : 0;
    const growth = 15.3; // Mock growth
    
    let format: 'currency' | 'number' | 'percentage' = 'number';
    if (currentChartType.value === 'revenue') format = 'currency';
    
    const currentTypeLabel = chartTypes.find(t => t.key === currentChartType.value)?.label || 'Data';
    
    return [
        {
            key: 'total',
            label: `Total ${currentTypeLabel}`,
            value: Math.round(total),
            format
        },
        {
            key: 'average',
            label: 'Daily Average',
            value: Math.round(average),
            format
        },
        {
            key: 'peak',
            label: 'Peak Day',
            value: Math.round(peak),
            format
        },
        {
            key: 'growth',
            label: 'Growth Rate',
            value: growth,
            format: 'percentage'
        }
    ];
});

// Event handlers
const onChartTypeChanged = (type: string) => {
    currentChartType.value = type;

    // Trigger data fetch for the new chart type if not already loaded
    if (type === 'revenue' && !revenueData.value) {
        refreshRevenue();
    } else if (type === 'bookings' && !bookingsData.value) {
        refreshBookings();
    }
};

const onDateRangeChanged = (dates: { start: Date; end: Date }) => {
    // In real app, refetch data for new date range with date parameters
    console.log('Date range changed:', dates);
    handleRetry();
};

const handleRetry = () => {
    // Refresh data based on current chart type
    if (currentChartType.value === 'revenue') {
        refreshRevenue();
    } else if (currentChartType.value === 'bookings') {
        refreshBookings();
    }
};

// Watch for chart type changes to ensure data is loaded
watch(currentChartType, (newType) => {
    if (newType === 'revenue' && !revenueData.value) {
        refreshRevenue();
    } else if (newType === 'bookings' && !bookingsData.value) {
        refreshBookings();
    }
});

// Load initial data on mount
onMounted(() => {
    // Load revenue data by default since it's the initial chart type
    if (!revenueData.value) {
        refreshRevenue();
    }
});
</script>
