<template>
    <AnalyticsBaseTrendsChart
        title="Business Performance Trends"
        subtitle="Track key business metrics and performance over time"
        :chart-types="chartTypes"
        :data="chartData"
        :is-loading="isLoading"
        :error="error"
        :summary-stats="summaryStats"
        :colors="['#3B82F6', '#10B981', '#F59E0B', '#EF4444']"
        @chart-type-changed="onChartTypeChanged"
        @date-range-changed="onDateRangeChanged"
        @retry="fetchTrendData"
    />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { format, subDays } from 'date-fns';
import type { ChartType, SummaryStatConfig, ChartDataPoint } from '~/components/Analytics/BaseTrendsChart.vue';
import { useOrganizationStore } from '~/stores/organization/organization.store';

const isLoading = ref(false);
const error = ref<string | null>(null);
const currentChartType = ref('revenue');
const organizationStore = useOrganizationStore()

const {} = useAsyncData('dashboard-trends', () => organizationStore.getEarningsTrend(), { lazy: true })

// Chart types
const chartTypes: ChartType[] = [
    { key: 'revenue', label: 'Revenue' },
    { key: 'bookings', label: 'Bookings' },
];

// Generate mock trend data (in real app, fetch from API)
const generateTrendData = (type: string): ChartDataPoint[] => {
    return Array.from({ length: 30 }, (_, i) => {
        const date = format(subDays(new Date(), 29 - i), 'yyyy-MM-dd');
        let value = 0;
        
        switch (type) {
            case 'revenue':
                value = Math.floor(Math.random() * 5000) + 2000; // $2000-7000
                break;
            case 'bookings':
                value = Math.floor(Math.random() * 100) + 50; // 50-150 bookings
                break;
        }
        
        return { x: date, y: value };
    });
};

const chartData = computed(() => generateTrendData(currentChartType.value));

// Summary statistics
const summaryStats = computed<SummaryStatConfig[]>(() => {
    const data = chartData.value;
    const values = data.map(d => d.y);
    
    const total = values.reduce((sum, val) => sum + val, 0);
    const average = values.length > 0 ? total / values.length : 0;
    const peak = values.length > 0 ? Math.max(...values) : 0;
    const growth = 15.3; // Mock growth
    
    let format: 'currency' | 'number' | 'percentage' = 'number';
    if (currentChartType.value === 'revenue') format = 'currency';
    
    const currentTypeLabel = chartTypes.find(t => t.key === currentChartType.value)?.label || 'Data';
    
    return [
        {
            key: 'total',
            label: `Total ${currentTypeLabel}`,
            value: Math.round(total),
            format
        },
        {
            key: 'average',
            label: 'Daily Average',
            value: Math.round(average),
            format
        },
        {
            key: 'peak',
            label: 'Peak Day',
            value: Math.round(peak),
            format
        },
        {
            key: 'growth',
            label: 'Growth Rate',
            value: growth,
            format: 'percentage'
        }
    ];
});

// Event handlers
const onChartTypeChanged = (type: string) => {
    currentChartType.value = type;
};

const onDateRangeChanged = (dates: { start: Date; end: Date }) => {
    // In real app, refetch data for new date range
    console.log('Date range changed:', dates);
    fetchTrendData();
};

const fetchTrendData = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        // Data is already computed reactively
    } catch (err) {
        error.value = 'Failed to load trend data';
    } finally {
        isLoading.value = false;
    }
};

onMounted(() => {
    fetchTrendData();
});
</script>
