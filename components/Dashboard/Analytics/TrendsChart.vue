<template>
    <AnalyticsBaseTrendsChart title="Business Performance Trends"
        subtitle="Track key business metrics and performance over time" :chart-types="chartTypes" :data="chartData"
        :is-loading="isLoading" :error="error" :summary-stats="summaryStats"
        :colors="['#3B82F6', '#10B981', '#F59E0B', '#EF4444']" @chart-type-changed="onChartTypeChanged"
        @date-range-changed="onDateRangeChanged" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import type { ChartType, SummaryStatConfig, ChartDataPoint } from '~/components/Analytics/BaseTrendsChart.vue';
import { useOrganizationStore } from '~/stores/organization/organization.store';

const organizationStore = useOrganizationStore();
const currentChartType = ref('revenue');
const isLoading = organizationStore.trends.loading;
const error = ref<string | null>(null);

// Chart types
const chartTypes: ChartType[] = [
    { key: 'revenue', label: 'Revenue' },
    { key: 'bookings', label: 'Bookings' },
];

// Computed chart data based on current type
const chartData = computed(() => {
    if (currentChartType.value === 'revenue') {
        return organizationStore.trends.earnings;
    } else if (currentChartType.value === 'bookings') {
        return organizationStore.trends.bookings;
    }
    return [];
});

// Summary statistics
const summaryStats = computed<SummaryStatConfig[]>(() => {

    let format: 'currency' | 'number' | 'percentage' = 'number';
    if (currentChartType.value === 'revenue') format = 'currency';

    const currentTypeLabel = chartTypes.find(t => t.key === currentChartType.value)?.label || 'Data';

    return [
        {
            key: 'total',
            label: `Total ${currentTypeLabel}`,
            value: organizationStore.trend_stats.data?.total || 0,
            format
        },
        {
            key: 'average',
            label: 'Daily Average',
            value: organizationStore.trend_stats.data?.average || 0,
            format
        },
        {
            key: 'peak',
            label: 'Peak Day',
            value: organizationStore.trend_stats.data?.peak || 0,
            format
        },
        {
            key: 'growth',
            label: 'Growth Rate',
            value: organizationStore.trend_stats.data?.growth || 0,
            format: 'percentage'
        }
    ];
});


// Load data for the new chart type if not already loaded
const getChartData = (type: string) => {
    // Load data for the new chart type if not already loaded
    if (type === 'revenue') {
        // Load revenue data
        organizationStore.getEarningsTrend().catch((err) => {
            console.error('Error fetching earnings trend:', err);
        });
    } else if (type === 'bookings') {
        // Load bookings data
        organizationStore.getBookingsTrend().catch((err) => {
            console.error('Error fetching bookings trend:', err);
        });
    }
};


// Event handlers
const onChartTypeChanged = async (type: string) => {
    console.log('Chart type changed to:', type);
    currentChartType.value = type;

    getChartData(type);
};

const onDateRangeChanged = (dates: { start: Date; end: Date }) => {
    // In real app, refetch data for new date range with date parameters
    console.log('Date range changed:', dates);

    getChartData(currentChartType.value);
};




// Load initial data on mount
onMounted(async () => {
    console.log('Component mounted, loading initial revenue data...');

    // Load revenue data by default since it's the initial chart type
    getChartData('revenue');
});
</script>
