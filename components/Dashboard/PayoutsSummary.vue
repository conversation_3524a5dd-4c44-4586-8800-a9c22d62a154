<template>
  <div class="p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Payouts Summary</h3>
      <NuxtLink
        to="/dashboard/revenue"
        class="text-sm text-blue-600 hover:text-blue-800"
      >
        View Details →
      </NuxtLink>
    </div>

    <div v-if="isLoading" class="space-y-4">
      <div class="animate-pulse">
        <div class="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div class="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
      <div class="space-y-2">
        <div v-for="i in 3" :key="i" class="animate-pulse">
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
            <div class="h-4 bg-gray-200 rounded w-1/3"></div>
            <div class="h-4 bg-gray-200 rounded w-1/4"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else>
      <!-- Total Payout Amount -->
      <div class="mb-6">
        <div class="flex items-baseline space-x-2">
          <p class="text-3xl font-bold text-gray-900">
            ₦{{ formatAmount(totalPayouts) }}
          </p>
          <span
            class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
            :class="{
              'bg-green-100 text-green-800': payoutTrend > 0,
              'bg-red-100 text-red-800': payoutTrend < 0,
              'bg-gray-100 text-gray-800': payoutTrend === 0
            }"
          >
            <TrendingUp v-if="payoutTrend > 0" class="w-3 h-3 mr-1" />
            <TrendingDown v-else-if="payoutTrend < 0" class="w-3 h-3 mr-1" />
            {{ payoutTrend > 0 ? '+' : '' }}{{ payoutTrend.toFixed(1) }}%
          </span>
        </div>
        <p class="text-sm text-gray-500 mt-1">Total payouts this month</p>
      </div>

      <!-- Payout Breakdown -->
      <div class="space-y-3">
        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle class="w-4 h-4 text-green-600" />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">Completed</p>
              <p class="text-xs text-gray-500">{{ completedPayouts }} payouts</p>
            </div>
          </div>
          <p class="text-sm font-semibold text-green-600">
            ₦{{ formatAmount(completedAmount) }}
          </p>
        </div>

        <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <Clock class="w-4 h-4 text-yellow-600" />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">Pending</p>
              <p class="text-xs text-gray-500">{{ pendingPayouts }} payouts</p>
            </div>
          </div>
          <p class="text-sm font-semibold text-yellow-600">
            ₦{{ formatAmount(pendingAmount) }}
          </p>
        </div>

        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <Calendar class="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">Scheduled</p>
              <p class="text-xs text-gray-500">Next payout in {{ nextPayoutDays }} days</p>
            </div>
          </div>
          <p class="text-sm font-semibold text-blue-600">
            ₦{{ formatAmount(scheduledAmount) }}
          </p>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="mt-6 pt-4 border-t border-gray-200">
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center">
            <p class="text-lg font-bold text-gray-900">{{ averagePayoutAmount }}</p>
            <p class="text-xs text-gray-500">Avg. Payout</p>
          </div>
          <div class="text-center">
            <p class="text-lg font-bold text-gray-900">{{ totalPayoutCount }}</p>
            <p class="text-xs text-gray-500">Total Payouts</p>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="totalPayouts === 0" class="text-center py-8">
        <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <DollarSign class="w-8 h-8 text-gray-400" />
        </div>
        <h3 class="text-sm font-medium text-gray-900 mb-1">No payouts yet</h3>
        <p class="text-sm text-gray-500">Payout information will appear here.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  CheckCircle, 
  Clock, 
  Calendar, 
  TrendingUp, 
  TrendingDown, 
  DollarSign 
} from 'lucide-vue-next'

interface Props {
  totalPayouts?: number
  payoutTrend?: number
  completedPayouts?: number
  completedAmount?: number
  pendingPayouts?: number
  pendingAmount?: number
  scheduledAmount?: number
  nextPayoutDays?: number
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  totalPayouts: 0,
  payoutTrend: 0,
  completedPayouts: 0,
  completedAmount: 0,
  pendingPayouts: 0,
  pendingAmount: 0,
  scheduledAmount: 0,
  nextPayoutDays: 7,
  isLoading: false
})

const totalPayoutCount = computed(() => 
  props.completedPayouts + props.pendingPayouts
)

const averagePayoutAmount = computed(() => {
  if (totalPayoutCount.value === 0) return '₦0'
  const avg = props.totalPayouts / totalPayoutCount.value
  return `₦${formatAmount(avg)}`
})

const formatAmount = (amount: number) => {
  if (!amount) return '0.00'
  return amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
</script>
