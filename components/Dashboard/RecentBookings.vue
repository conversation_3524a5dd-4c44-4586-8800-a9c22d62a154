<template>
  <div class="space-y-4">
    <div v-if="organizationStore.recent_bookings.loading" class="space-y-3">
      <div v-for="i in 3" :key="i" class="animate-pulse">
        <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
          <div class="w-12 h-12 bg-gray-200 rounded-full"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div class="w-16 h-6 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>

    <div v-else-if="organizationStore.recent_bookings.data.length === 0" class="text-center py-8">
      <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <Calendar class="w-8 h-8 text-gray-400" />
      </div>
      <h3 class="text-sm font-medium text-gray-900 mb-1">No recent bookings</h3>
      <p class="text-sm text-gray-500">New bookings will appear here when they're made.</p>
    </div>

    <div v-else class="space-y-3">
      <div
        v-for="booking in organizationStore.recent_bookings.data.slice(0, 5)"
        :key="booking.id"
        class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
        @click="viewBookingDetails(booking)"
      >
        <!-- Status Indicator -->
        <div class="flex-shrink-0">
          <div
            class="w-3 h-3 rounded-full"
            :class="{
              'bg-green-500': booking.status === 'confirmed',
              'bg-yellow-500': booking.status === 'pending',
              'bg-red-500': booking.status === 'cancelled',
              'bg-blue-500': booking.status === 'completed',
              'bg-gray-400': !['confirmed', 'pending', 'cancelled', 'completed'].includes(booking.status)
            }"
          ></div>
        </div>

        <!-- Booking Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 truncate">
              {{ booking.booking_reference }}
            </p>
            <span
              class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
              :class="{
                'bg-green-100 text-green-800': booking.status === 'confirmed',
                'bg-yellow-100 text-yellow-800': booking.status === 'pending',
                'bg-red-100 text-red-800': booking.status === 'cancelled',
                'bg-blue-100 text-blue-800': booking.status === 'completed',
                'bg-gray-100 text-gray-800': !['confirmed', 'pending', 'cancelled', 'completed'].includes(booking.status)
              }"
            >
              {{ booking.status }}
            </span>
          </div>
          <div class="flex items-center space-x-4 mt-1">
            <p class="text-sm text-gray-500 truncate">
              {{ booking.route?.origin.name }} → {{ booking.route?.destination.name }}
            </p>
            <p class="text-xs text-gray-400">
              {{ formatDate(booking.booking_schedule_date) }}
            </p>
          </div>
        </div>

        <!-- Amount -->
        <div class="flex-shrink-0 text-right">
          <p class="text-sm font-semibold text-gray-900">
            GHC{{ formatAmount(booking.total_amount) }}
          </p>
          <p class="text-xs text-gray-500">
            {{ booking.number_of_tickets }} ticket{{ booking.number_of_tickets !== 1 ? 's' : '' }}
          </p>
        </div>

        <!-- Arrow -->
        <div class="flex-shrink-0">
          <ChevronRight class="w-4 h-4 text-gray-400" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Calendar, ChevronRight } from 'lucide-vue-next'
import type { IBooking } from '~/stores/booking/model/booking.model'
import { useOrganizationStore } from '~/stores/organization/organization.store'

const organizationStore = useOrganizationStore();


const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return 'Invalid date'
  }
}

const formatAmount = (amount: number | string) => {
  if (!amount) return '0.00'
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// Get recent bookings
 const { data: recentBookings, status: bookingsLoading } = useAsyncData('recent-bookings', () => organizationStore.getRecentBookings(), { lazy: true })

const viewBookingDetails = (booking: IBooking) => {
  // Navigate to booking details or open modal
  navigateTo(`/dashboard/bookings?booking=${booking.id}`)
}
</script>
