<template>
    <div :class="[
        'h-full overflow-y-auto',
        isSheet ? 'w-full bg-transparent' : 'w-80 bg-white border-l border-gray-200'
    ]">
        <!-- Header (only show if not in sheet mode) -->
        <div v-if="!isSheet" class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900">{{ title }}</h2>
                <Button variant="ghost" size="sm" @click="$emit('close')">
                    <X class="h-4 w-4" />
                </Button>
            </div>
            <p class="text-sm text-gray-500 mt-1">{{ subtitle }}</p>
        </div>

        <!-- Quick Stats -->
        <div v-if="todayStats" :class="[
            'border-b border-gray-200',
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Today's Overview</h3>
            <div class="space-y-4">
                <div v-for="stat in todayStats" :key="stat.key" class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div :class="[
                            'w-2 h-2 rounded-full',
                            stat.color || 'bg-blue-500'
                        ]"></div>
                        <span class="text-sm text-gray-600">{{ stat.label }}</span>
                    </div>
                    <span class="text-sm font-semibold text-gray-900">{{ formatValue(stat.value, stat.format) }}</span>
                </div>
            </div>
        </div>

        <!-- Alerts & Issues -->
        <div v-if="alerts && alerts.length > 0" :class="[
            'border-b border-gray-200',
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Alerts & Issues</h3>
            <div class="space-y-3">
                <div v-for="alert in alerts" :key="alert.id" 
                     :class="[
                         'p-3 rounded-lg border-l-4',
                         alert.type === 'error' ? 'bg-red-50 border-red-400' :
                         alert.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
                         'bg-blue-50 border-blue-400'
                     ]">
                    <div class="flex items-start space-x-2">
                        <component 
                            :is="alert.icon" 
                            :class="[
                                'h-4 w-4 mt-0.5',
                                alert.type === 'error' ? 'text-red-500' :
                                alert.type === 'warning' ? 'text-yellow-500' :
                                'text-blue-500'
                            ]" 
                        />
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">{{ alert.title }}</p>
                            <p class="text-xs text-gray-600 mt-1">{{ alert.message }}</p>
                            <p class="text-xs text-gray-500 mt-1">{{ alert.time }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Issues State -->
        <div v-else-if="alerts" :class="[
            'border-b border-gray-200',
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Alerts & Issues</h3>
            <div class="text-center py-4">
                <CheckCircle class="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p class="text-sm text-gray-500">No issues detected</p>
            </div>
        </div>

        <!-- Recent Activity -->
        <div v-if="recentActivity" :class="[
            'border-b border-gray-200',
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div class="space-y-3">
                <div v-for="activity in recentActivity" :key="activity.id" class="flex items-start space-x-3">
                    <div :class="activity.iconColor" class="p-1 rounded-full">
                        <component :is="activity.icon" class="h-3 w-3" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-xs text-gray-900">{{ activity.message }}</p>
                        <p class="text-xs text-gray-500">{{ activity.time }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div v-if="quickActions" :class="[
            'border-b border-gray-200',
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-2">
                <Button 
                    v-for="action in quickActions" 
                    :key="action.key"
                    variant="outline" 
                    size="sm" 
                    class="w-full justify-start" 
                    @click="$emit('quickAction', action.key)"
                >
                    <component :is="action.icon" class="h-4 w-4 mr-2" />
                    {{ action.label }}
                </Button>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div v-if="performanceMetrics" :class="[
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Performance</h3>
            <div class="space-y-4">
                <div v-for="metric in performanceMetrics" :key="metric.key">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-gray-600">{{ metric.label }}</span>
                        <span class="text-xs font-semibold text-gray-900">{{ formatValue(metric.value, metric.format) }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div
                            :class="[
                                'h-2 rounded-full',
                                metric.color || 'bg-blue-500'
                            ]"
                            :style="{ width: `${Math.min(metric.percentage || (metric.max ? (metric.value / metric.max * 100) : metric.value), 100)}%` }"
                        ></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { X, CheckCircle } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';

export interface TodayStatConfig {
    key: string;
    label: string;
    value: number;
    format?: 'number' | 'currency' | 'percentage';
    color?: string;
}

export interface AlertConfig {
    id: string | number;
    type: 'error' | 'warning' | 'info';
    title: string;
    message: string;
    time: string;
    icon: any;
}

export interface ActivityConfig {
    id: string | number;
    message: string;
    time: string;
    icon: any;
    iconColor: string;
}

export interface QuickActionConfig {
    key: string;
    label: string;
    icon: any;
}

export interface PerformanceMetricConfig {
    key: string;
    label: string;
    value: number;
    format?: 'number' | 'currency' | 'percentage';
    color?: string;
    percentage?: number;
    max?: number;
}

const props = defineProps<{
    title: string;
    subtitle: string;
    isSheet?: boolean;
    todayStats?: TodayStatConfig[];
    alerts?: AlertConfig[];
    recentActivity?: ActivityConfig[];
    quickActions?: QuickActionConfig[];
    performanceMetrics?: PerformanceMetricConfig[];
}>();

const emit = defineEmits<{
    (e: 'close'): void;
    (e: 'quickAction', actionKey: string): void;
}>();

// Format value helper
const formatValue = (value: number, format?: string) => {
    switch (format) {
        case 'currency':
            return '$' + value.toLocaleString();
        case 'percentage':
            return value.toFixed(1) + '%';
        case 'number':
        default:
            return value.toLocaleString();
    }
};
</script>
