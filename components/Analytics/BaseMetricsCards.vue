<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- Show skeletons when loading -->
    <template v-if="loading">
      <div
        v-for="i in 4"
        :key="i"
        class="bg-white rounded-lg border border-gray-200 p-6 animate-pulse"
      >
        <div class="flex items-center justify-between">
          <div>
            <div class="h-3 w-20 bg-gray-200 rounded mb-2"></div>
            <div class="h-6 w-28 bg-gray-300 rounded mb-2"></div>
            <div class="h-3 w-16 bg-gray-200 rounded"></div>
          </div>
          <div class="p-3 rounded-full bg-gray-100">
            <div class="h-6 w-6 bg-gray-300 rounded-full"></div>
          </div>
        </div>
      </div>
    </template>

    <!-- Actual metrics -->
    <template v-else>
      <div
        v-for="metric in metrics"
        :key="metric.key"
        class="bg-white rounded-lg border border-gray-200 p-6"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">{{ metric.label }}</p>
            <p class="text-2xl font-bold text-gray-900">
              {{ formatValue(metric.value, metric.format) }}
            </p>
            <p
              v-if="metric.trend !== undefined"
              class="text-xs text-gray-500 mt-1"
            >
              <span
                :class="metric.trend >= 0 ? 'text-green-600' : 'text-red-600'"
              >
                {{ metric.trend >= 0 ? '+' : '' }}{{ metric.trend }}%
              </span>
              {{ metric.trendLabel || 'vs last month' }}
            </p>
          </div>
          <div
            :class="[
              'p-3 rounded-full',
              metric.iconBg || 'bg-blue-50'
            ]"
          >
            <component
              :is="metric.icon"
              :class="[
                'h-6 w-6',
                metric.iconColor || 'text-blue-600'
              ]"
            />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

export interface MetricConfig {
  key: string;
  label: string;
  value: number;
  format?: 'number' | 'currency' | 'percentage';
  trend?: number;
  trendLabel?: string;
  icon: any;
  iconBg?: string;
  iconColor?: string;
}

const props = defineProps<{
  metrics: MetricConfig[];
  loading?: boolean; // new prop
}>();

const formatValue = (value: number, format?: string) => {
  switch (format) {
    case 'currency':
      return `GHC${value.toLocaleString()}`;
    case 'percentage':
      return `${value.toFixed(1)}%`;
    case 'number':
    default:
      return value.toLocaleString();
  }
};
</script>
