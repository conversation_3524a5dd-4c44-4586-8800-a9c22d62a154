<template>
    <NuxtLayout name="dashboard">
        <div class="min-h-screen bg-gray-50">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200">
                <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <div class="py-6 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
                            <p class="mt-1 text-sm text-gray-500">{{ subtitle }}</p>
                        </div>
                        <div class="flex items-center space-x-3 mt-4 sm:mt-0">
                            <Button variant="outline" @click="toggleInsights">
                                <BarChart3 class="h-4 w-4 mr-2" />
                                {{ showInsights ? 'Hide' : 'Show' }} Insights
                            </Button>
                            <slot name="header-actions">
                                <Button>
                                    <Plus class="h-4 w-4 mr-2" />
                                    {{ newButtonText }}
                                </Button>
                            </slot>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8 py-6">
                <!-- Analytics Dashboard -->
                <Suspense>
                    <template #default>
                        <div class="space-y-6">
                            <!-- Metrics Cards -->
                            <slot name="metrics-cards" />
                            
                            <!-- Trends Chart -->
                            <slot name="trends-chart" />
                            
                            <!-- Filters -->
                            <slot name="filters" />
                            
                            <!-- Main Data Table -->
                            <div class="bg-white rounded-lg border border-gray-200">
                                <div class="p-6 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-900">{{ tableTitle }}</h3>
                                            <p class="text-sm text-gray-500">{{ tableSubtitle }}</p>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <slot name="table-actions">
                                                <Button variant="outline" size="sm" @click="$emit('export')">
                                                    <Download class="h-4 w-4 mr-2" />
                                                    Export
                                                </Button>
                                            </slot>
                                        </div>
                                    </div>
                                </div>
                                <slot name="data-table" />
                            </div>
                        </div>
                    </template>
                    <template #fallback>
                        <div class="w-full h-96 flex justify-center items-center">
                            <div class="flex flex-col items-center">
                                <Loader2 class="animate-spin h-8 w-8 mb-2" />
                                <p class="text-gray-500">{{ loadingText }}</p>
                            </div>
                        </div>
                    </template>
                </Suspense>
            </div>

            <!-- Details Sheet -->
            <Sheet :open="isDetailsSheetOpen" @update:open="handleDetailsSheetChange">
                <SheetContent
                    :class="[
                        'h-full bg-white overflow-y-auto',
                        isMobile ? 'fixed inset-x-0 bottom-0 mt-24 rounded-t-[10px]' : 'sm:max-w-[800px] rounded-lg'
                    ]"
                    :side="isMobile ? 'bottom' : 'right'"
                >
                    <SheetHeader>
                        <SheetTitle>{{ detailsSheetTitle }}</SheetTitle>
                        <SheetDescription>{{ detailsSheetDescription }}</SheetDescription>
                    </SheetHeader>
                    <div class="px-4 py-6">
                        <slot name="details-content" />
                    </div>
                </SheetContent>
            </Sheet>

            <!-- Insights Sheet -->
            <Sheet :open="showInsights" @update:open="handleInsightsSheetChange">
                <SheetContent
                    :class="[
                        'h-full bg-white overflow-y-auto',
                        isMobile ? 'fixed inset-x-0 bottom-0 mt-24 rounded-t-[10px]' : 'sm:max-w-[900px] rounded-lg'
                    ]"
                    :side="isMobile ? 'bottom' : 'right'"
                >
                    <SheetHeader>
                        <SheetTitle>{{ insightsSheetTitle }}</SheetTitle>
                        <SheetDescription>{{ insightsSheetDescription }}</SheetDescription>
                    </SheetHeader>
                    <div class="p-0">
                        <slot name="insights-content" />
                    </div>
                </SheetContent>
            </Sheet>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { BarChart3, Plus, Download, Loader2 } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetTitle
} from '@/components/ui/sheet';

const props = defineProps<{
    title: string;
    subtitle: string;
    newButtonText?: string;
    tableTitle: string;
    tableSubtitle: string;
    loadingText?: string;
    detailsSheetTitle?: string;
    detailsSheetDescription?: string;
    insightsSheetTitle?: string;
    insightsSheetDescription?: string;
}>();

const emit = defineEmits<{
    (e: 'export'): void;
    (e: 'detailsSheetChanged', isOpen: boolean): void;
    (e: 'insightsSheetChanged', isOpen: boolean): void;
}>();

// Check if the device is mobile
const isMobile = ref(false);
onMounted(() => {
    isMobile.value = window.innerWidth < 768;
    window.addEventListener('resize', () => {
        isMobile.value = window.innerWidth < 768;
    });
});

// Insights sheet state
const showInsights = ref(false);
const toggleInsights = () => {
    showInsights.value = !showInsights.value;
};

// Handle insights sheet open/close
const handleInsightsSheetChange = (isOpen: boolean) => {
    showInsights.value = isOpen;
    emit('insightsSheetChanged', isOpen);
};

// Details sheet state
const isDetailsSheetOpen = ref(false);
const handleDetailsSheetChange = (isOpen: boolean) => {
    isDetailsSheetOpen.value = isOpen;
    emit('detailsSheetChanged', isOpen);
};

// Expose methods for parent components
defineExpose({
    openDetailsSheet: () => { isDetailsSheetOpen.value = true; },
    closeDetailsSheet: () => { isDetailsSheetOpen.value = false; },
    openInsightsSheet: () => { showInsights.value = true; },
    closeInsightsSheet: () => { showInsights.value = false; }
});
</script>
