<template>
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">{{ title }}</h3>
                <p class="text-sm text-gray-500">{{ subtitle }}</p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Chart Type Toggle -->
                <div v-if="chartTypes.length > 1" class="flex bg-gray-100 rounded-lg p-1">
                    <button 
                        v-for="type in chartTypes"
                        :key="type.key"
                        @click="currentChartType = type.key"
                        :class="[
                            'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                            currentChartType === type.key 
                                ? 'bg-white text-gray-900 shadow-sm' 
                                : 'text-gray-600 hover:text-gray-900'
                        ]"
                    >
                        {{ type.label }}
                    </button>
                </div>
                
                <!-- Date Range Picker -->
                <DateRangePicker @handle-date-change="onDateChanged" />
            </div>
        </div>

        <div v-if="isLoading" class="w-full h-80 flex items-center justify-center">
            <Loader2 class="h-8 w-8 animate-spin text-primary" />
        </div>
        
        <div v-else-if="error" class="w-full h-80 flex items-center justify-center">
            <div class="text-center">
                <p class="text-red-500">{{ error }}</p>
                <Button variant="outline" size="sm" class="mt-2" @click="$emit('retry')">Retry</Button>
            </div>
        </div>
        
        <template v-else>
            <ClientOnly>
                <apexchart 
                    :key="chartOptions.series" 
                    height="320" 
                    width="100%" 
                    :options="chartOptions"
                    :series="chartOptions.series"
                />
            </ClientOnly>
        </template>

        <!-- Summary Stats -->
        <div v-if="summaryStats" class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-gray-200">
            <div v-for="stat in summaryStats" :key="stat.key" class="text-center">
                <p class="text-2xl font-bold text-gray-900">{{ formatValue(stat.value, stat.format) }}</p>
                <p class="text-sm text-gray-500">{{ stat.label }}</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Loader2 } from 'lucide-vue-next';
import { format } from 'date-fns';

export interface ChartType {
    key: string;
    label: string;
}

export interface SummaryStatConfig {
    key: string;
    label: string;
    value: number;
    format?: 'number' | 'currency' | 'percentage';
}

export interface ChartDataPoint {
    x: string;
    y: number;
}

const props = defineProps<{
    title: string;
    subtitle: string;
    chartTypes: ChartType[];
    data: ChartDataPoint[];
    isLoading?: boolean;
    error?: string | null;
    summaryStats?: SummaryStatConfig[];
    colors?: string[];
}>();

const emit = defineEmits<{
    (e: 'chartTypeChanged', type: string): void;
    (e: 'dateRangeChanged', dates: { start: Date; end: Date }): void;
    (e: 'retry'): void;
}>();

const currentChartType = ref(props.chartTypes[0]?.key || '');

// Chart options
const chartOptions = computed(() => ({
    chart: {
        type: 'area',
        height: 320,
        toolbar: {
            show: false,
        },
        zoom: {
            enabled: false,
        },
    },
    series: [{
        name: props.chartTypes.find(t => t.key === currentChartType.value)?.label || 'Data',
        data: props.data
    }],
    dataLabels: {
        enabled: false,
    },
    stroke: {
        show: true,
        curve: 'smooth',
        lineCap: 'butt',
        width: 3,
    },
    xaxis: {
        type: 'datetime',
        labels: {
            format: 'dd MMM',
        }
    },
    yaxis: {
        show: true,
        title: {
            text: props.chartTypes.find(t => t.key === currentChartType.value)?.label || 'Value',
        },
        min: 0,
        forceNiceScale: true,
    },
    tooltip: {
        enabled: true,
        x: {
            format: 'dd MMM yyyy'
        }
    },
    fill: {
        opacity: 0.3,
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: undefined,
            inverseColors: false,
            opacityFrom: 0.5,
            opacityTo: 0.1,
        }
    },
    colors: props.colors || ['#3B82F6'],
    grid: {
        show: true,
        borderColor: '#E5E7EB',
        strokeDashArray: 0,
        position: 'back',
        xaxis: {
            lines: {
                show: false
            }
        },
        yaxis: {
            lines: {
                show: true
            }
        }
    },
    legend: {
        show: false
    },
}));

// Handle date range changes
const onDateChanged = (dates: { start: Date; end: Date }) => {
    emit('dateRangeChanged', dates);
};

// Watch for chart type changes
watch(currentChartType, (newType) => {
    emit('chartTypeChanged', newType);
});

// Format value helper
const formatValue = (value: number, format?: string) => {
    switch (format) {
        case 'currency':
            return '$' + value.toLocaleString();
        case 'percentage':
            return value.toFixed(1) + '%';
        case 'number':
        default:
            return value.toLocaleString();
    }
};
</script>
