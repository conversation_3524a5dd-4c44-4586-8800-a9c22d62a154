<template>
    <AnalyticsBaseInsightsSidebar
        title="Customer Insights"
        subtitle="Customer behavior analytics and engagement insights"
        :is-sheet="isSheet"
        :today-stats="todayStats"
        :alerts="alerts"
        :recent-activity="recentActivity"
        :quick-actions="quickActions"
        :performance-metrics="performanceMetrics"
        @close="$emit('close')"
        @quick-action="handleQuickAction"
    />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    AlertTriangle, 
    Info, 
    Plus, 
    Minus, 
    Settings,
    Download,
    FileText,
    Users,
    UserPlus,
    Star,
    TrendingUp,
    Mail
} from 'lucide-vue-next';
import type { User } from '~/stores/auth/user/model/user.model';
import type {
    TodayStatConfig,
    AlertConfig,
    ActivityConfig,
    QuickActionConfig,
    PerformanceMetricConfig
} from '~/components/Analytics/BaseInsightsSidebar.vue';

const props = defineProps<{
    customers: User[];
    isSheet?: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
}>();

// Today's stats
const todayStats = computed<TodayStatConfig[]>(() => {
    const totalCustomers = props.customers.length;
    const activeCustomers = props.customers.filter(c => c.role?.name?.toLowerCase() === 'rider').length;
    const newCustomersToday = Math.floor(totalCustomers * 0.02); // Mock 2% new today
    const todayBookings = Math.floor(activeCustomers * 0.15); // Mock 15% booked today
    
    return [
        {
            key: 'total_customers',
            label: 'Total Customers',
            value: totalCustomers,
            color: 'bg-blue-500'
        },
        {
            key: 'active_customers',
            label: 'Active Customers',
            value: activeCustomers,
            color: 'bg-green-500'
        },
        {
            key: 'new_today',
            label: 'New Today',
            value: newCustomersToday,
            color: 'bg-purple-500'
        },
        {
            key: 'today_bookings',
            label: 'Bookings Today',
            value: todayBookings,
            color: 'bg-orange-500'
        }
    ];
});

// Alerts
const alerts = computed<AlertConfig[]>(() => {
    const alertsList: AlertConfig[] = [];
    
    const totalCustomers = props.customers.length;
    const activeCustomers = props.customers.filter(c => c.role?.name?.toLowerCase() === 'rider').length;
    const inactiveCustomers = totalCustomers - activeCustomers;
    
    if (inactiveCustomers > totalCustomers * 0.3) { // More than 30% inactive
        alertsList.push({
            id: 'inactive_customers',
            type: 'warning',
            title: 'High Inactive Customer Rate',
            message: `${inactiveCustomers} customers (${((inactiveCustomers/totalCustomers)*100).toFixed(1)}%) are inactive`,
            time: '1 hour ago',
            icon: AlertTriangle
        });
    }
    
    // Mock churn risk alert
    alertsList.push({
        id: 'churn_risk',
        type: 'info',
        title: 'Churn Risk Analysis',
        message: '23 customers identified as high churn risk - consider retention campaigns',
        time: '3 hours ago',
        icon: Info
    });
    
    return alertsList;
});

// Recent activity
const recentActivity = computed<ActivityConfig[]>(() => [
    {
        id: 1,
        message: 'New customer Sarah M. registered',
        time: '3 min ago',
        icon: UserPlus,
        iconColor: 'bg-green-100 text-green-600'
    },
    {
        id: 2,
        message: 'Customer John D. completed 5th booking',
        time: '12 min ago',
        icon: Star,
        iconColor: 'bg-yellow-100 text-yellow-600'
    },
    {
        id: 3,
        message: 'Customer satisfaction survey: 4.7/5.0',
        time: '25 min ago',
        icon: TrendingUp,
        iconColor: 'bg-blue-100 text-blue-600'
    },
    {
        id: 4,
        message: 'Retention campaign sent to 150 customers',
        time: '45 min ago',
        icon: Mail,
        iconColor: 'bg-purple-100 text-purple-600'
    }
]);

// Quick actions
const quickActions = computed<QuickActionConfig[]>(() => [
    {
        key: 'export_customers',
        label: 'Export Customer Data',
        icon: Download
    },
    {
        key: 'customer_report',
        label: 'Generate Customer Report',
        icon: FileText
    },
    {
        key: 'retention_campaign',
        label: 'Launch Retention Campaign',
        icon: Mail
    },
    {
        key: 'customer_settings',
        label: 'Customer Settings',
        icon: Settings
    }
]);

// Performance metrics
const performanceMetrics = computed<PerformanceMetricConfig[]>(() => [
    {
        key: 'acquisition_rate',
        label: 'Customer Acquisition Rate',
        value: 12.8,
        format: 'percentage',
        color: 'bg-green-500',
        percentage: 64 // 12.8% out of 20% target
    },
    {
        key: 'retention_rate',
        label: 'Customer Retention',
        value: 78.5,
        format: 'percentage',
        color: 'bg-blue-500',
        percentage: 78.5
    },
    {
        key: 'engagement_score',
        label: 'Engagement Score',
        value: 72.3,
        format: 'percentage',
        color: 'bg-purple-500',
        percentage: 72.3
    },
    {
        key: 'satisfaction_score',
        label: 'Satisfaction Score',
        value: 86.0,
        format: 'percentage',
        color: 'bg-orange-500',
        percentage: 86.0 // 4.3/5.0 = 86%
    }
]);

// Handle quick actions
const handleQuickAction = (actionKey: string) => {
    switch (actionKey) {
        case 'export_customers':
            console.log('Exporting customer data...');
            break;
        case 'customer_report':
            console.log('Generating customer report...');
            break;
        case 'retention_campaign':
            console.log('Launching retention campaign...');
            break;
        case 'customer_settings':
            console.log('Opening customer settings...');
            break;
    }
};
</script>
