<template>
    <AnalyticsBaseMetricsCards :metrics="customerMetrics" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    Users, 
    UserPlus, 
    TrendingUp, 
    DollarSign, 
    Calendar,
    Star,
    Activity,
    Target
} from 'lucide-vue-next';
import type { User } from '~/stores/auth/user/model/user.model';
import type { MetricConfig } from '~/components/Analytics/BaseMetricsCards.vue';

const props = defineProps<{
    customers: User[];
}>();

const customerMetrics = computed<MetricConfig[]>(() => {
    const totalCustomers = props.customers.length;
    const activeCustomers = props.customers.filter(c => c.role?.name?.toLowerCase() === 'rider').length;
    
    // Mock calculations (in real app, get from bookings/analytics API)
    const newCustomersThisMonth = Math.floor(totalCustomers * 0.15); // 15% new this month
    const averageBookingsPerCustomer = 4.2;
    const customerLifetimeValue = 245.50;
    const retentionRate = 78.5;
    const satisfactionScore = 4.3;
    const monthlyActiveUsers = Math.floor(totalCustomers * 0.65); // 65% monthly active

    return [
        {
            key: 'total_customers',
            label: 'Total Customers',
            value: totalCustomers,
            format: 'number',
            trend: 12.8,
            icon: Users,
            iconBg: 'bg-blue-50',
            iconColor: 'text-blue-600'
        },
        {
            key: 'active_customers',
            label: 'Active Customers',
            value: activeCustomers,
            format: 'number',
            trend: 8.4,
            icon: Activity,
            iconBg: 'bg-green-50',
            iconColor: 'text-green-600'
        },
        {
            key: 'new_customers',
            label: 'New This Month',
            value: newCustomersThisMonth,
            format: 'number',
            trend: 15.7,
            trendLabel: 'vs last month',
            icon: UserPlus,
            iconBg: 'bg-emerald-50',
            iconColor: 'text-emerald-600'
        },
        {
            key: 'monthly_active',
            label: 'Monthly Active Users',
            value: monthlyActiveUsers,
            format: 'number',
            trend: 6.2,
            icon: Calendar,
            iconBg: 'bg-purple-50',
            iconColor: 'text-purple-600'
        },
        {
            key: 'avg_bookings',
            label: 'Avg Bookings/Customer',
            value: averageBookingsPerCustomer,
            format: 'number',
            trend: 3.8,
            icon: Target,
            iconBg: 'bg-orange-50',
            iconColor: 'text-orange-600'
        },
        {
            key: 'lifetime_value',
            label: 'Customer Lifetime Value',
            value: customerLifetimeValue,
            format: 'currency',
            trend: 9.1,
            icon: DollarSign,
            iconBg: 'bg-indigo-50',
            iconColor: 'text-indigo-600'
        },
        {
            key: 'retention_rate',
            label: 'Retention Rate',
            value: retentionRate,
            format: 'percentage',
            trend: 4.5,
            icon: TrendingUp,
            iconBg: 'bg-teal-50',
            iconColor: 'text-teal-600'
        },
        {
            key: 'satisfaction_score',
            label: 'Satisfaction Score',
            value: satisfactionScore,
            format: 'number',
            trend: 2.3,
            trendLabel: 'out of 5.0',
            icon: Star,
            iconBg: 'bg-rose-50',
            iconColor: 'text-rose-600'
        }
    ];
});
</script>
