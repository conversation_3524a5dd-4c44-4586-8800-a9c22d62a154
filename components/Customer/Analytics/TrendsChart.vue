<template>
    <AnalyticsBaseTrendsChart
        title="Customer Analytics & Behavior"
        subtitle="Track customer acquisition, retention, and engagement trends"
        :chart-types="chartTypes"
        :data="chartData"
        :is-loading="isLoading"
        :error="error"
        :summary-stats="summaryStats"
        :colors="['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6']"
        @chart-type-changed="onChartTypeChanged"
        @date-range-changed="onDateRangeChanged"
        @retry="fetchTrendData"
    />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { format, subDays } from 'date-fns';
import type { User } from '~/stores/auth/user/model/user.model';
import type { ChartType, SummaryStatConfig, ChartDataPoint } from '~/components/Analytics/BaseTrendsChart.vue';

const props = defineProps<{
    customers: User[];
}>();

const isLoading = ref(false);
const error = ref<string | null>(null);
const currentChartType = ref('acquisition');

// Chart types
const chartTypes: ChartType[] = [
    { key: 'acquisition', label: 'Customer Acquisition' },
    { key: 'retention', label: 'Retention Rate' },
    { key: 'engagement', label: 'Engagement Score' },
    { key: 'satisfaction', label: 'Satisfaction Score' }
];

// Generate mock trend data (in real app, fetch from API)
const generateTrendData = (type: string): ChartDataPoint[] => {
    return Array.from({ length: 30 }, (_, i) => {
        const date = format(subDays(new Date(), 29 - i), 'yyyy-MM-dd');
        let value = 0;
        
        switch (type) {
            case 'acquisition':
                value = Math.floor(Math.random() * 20) + 5; // 5-25 new customers per day
                break;
            case 'retention':
                value = Math.floor(Math.random() * 20) + 70; // 70-90% retention
                break;
            case 'engagement':
                value = Math.floor(Math.random() * 30) + 60; // 60-90% engagement
                break;
            case 'satisfaction':
                value = (Math.random() * 1.5 + 3.5); // 3.5-5.0 satisfaction score
                break;
        }
        
        return { x: date, y: value };
    });
};

const chartData = computed(() => generateTrendData(currentChartType.value));

// Summary statistics
const summaryStats = computed<SummaryStatConfig[]>(() => {
    const data = chartData.value;
    const values = data.map(d => d.y);
    
    const total = values.reduce((sum, val) => sum + val, 0);
    const average = values.length > 0 ? total / values.length : 0;
    const peak = values.length > 0 ? Math.max(...values) : 0;
    const growth = 12.8; // Mock growth
    
    let format: 'currency' | 'number' | 'percentage' = 'number';
    if (currentChartType.value === 'retention' || currentChartType.value === 'engagement') {
        format = 'percentage';
    }
    
    const currentTypeLabel = chartTypes.find(t => t.key === currentChartType.value)?.label || 'Data';
    
    return [
        {
            key: 'total_customers',
            label: 'Total Customers',
            value: props.customers.length,
            format: 'number'
        },
        {
            key: 'average',
            label: `Avg ${currentTypeLabel}`,
            value: Math.round(average * 100) / 100,
            format
        },
        {
            key: 'peak',
            label: 'Peak Performance',
            value: Math.round(peak * 100) / 100,
            format
        },
        {
            key: 'growth',
            label: 'Growth Rate',
            value: growth,
            format: 'percentage'
        }
    ];
});

// Event handlers
const onChartTypeChanged = (type: string) => {
    currentChartType.value = type;
};

const onDateRangeChanged = (dates: { start: Date; end: Date }) => {
    // In real app, refetch data for new date range
    console.log('Date range changed:', dates);
    fetchTrendData();
};

const fetchTrendData = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        // Data is already computed reactively
    } catch (err) {
        error.value = 'Failed to load customer trend data';
    } finally {
        isLoading.value = false;
    }
};

onMounted(() => {
    fetchTrendData();
});
</script>
