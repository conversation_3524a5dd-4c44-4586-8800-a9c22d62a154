<template>
  <Sidebar variant="inset">
    <SidebarHeader>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" asChild>
            <NuxtLink to="/dashboard">
              
              <div class="grid flex-1 text-left text-sm leading-tight">
              <div>
                <img src="/img/logo_b.png" alt="Kwanso Shuttle" class="h-8 w-auto" />
              </div>
                <span class="truncate text-xs">Admin Dashboard</span>
              </div>
            </NuxtLink>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>
    
    <SidebarContent>
      <!-- Main Navigation -->
      <SidebarGroup>
        <SidebarGroupLabel>Overview</SidebarGroupLabel>
        <SidebarMenu>
          <SidebarMenuItem v-for="item in mainNavItems" :key="item.title">
            <SidebarMenuButton asChild :isActive="isCurrentRoute(item.url)">
              <NuxtLink :to="item.url" class="flex items-center gap-2">
                <component :is="item.icon" class="size-4" />
                <span>{{ item.title }}</span>
              </NuxtLink>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroup>

      <!-- Operations -->
      <SidebarGroup>
        <SidebarGroupLabel>Operations</SidebarGroupLabel>
        <SidebarMenu>
          <SidebarMenuItem v-for="item in operationsItems" :key="item.title">
            <SidebarMenuButton asChild :isActive="isCurrentRoute(item.url)">
              <NuxtLink :to="item.url" class="flex items-center gap-2">
                <component :is="item.icon" class="size-4" />
                <span>{{ item.title }}</span>
                <SidebarMenuBadge v-if="item.badge" class="ml-auto">
                  {{ item.badge }}
                </SidebarMenuBadge>
              </NuxtLink>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroup>

      <!-- Management -->
      <SidebarGroup>
        <SidebarGroupLabel>Management</SidebarGroupLabel>
        <SidebarMenu>
          <SidebarMenuItem v-for="item in managementItems" :key="item.title">
            <SidebarMenuButton asChild :isActive="isCurrentRoute(item.url)">
              <NuxtLink :to="item.url" class="flex items-center gap-2">
                <component :is="item.icon" class="size-4" />
                <span>{{ item.title }}</span>
                <SidebarMenuBadge v-if="item.badge" class="ml-auto">
                  {{ item.badge }}
                </SidebarMenuBadge>
              </NuxtLink>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroup>

      <!-- Analytics -->
      <SidebarGroup>
        <SidebarGroupLabel>Analytics</SidebarGroupLabel>
        <SidebarMenu>
          <SidebarMenuItem v-for="item in analyticsItems" :key="item.title">
            <SidebarMenuButton asChild :isActive="isCurrentRoute(item.url)">
              <NuxtLink :to="item.url" class="flex items-center gap-2">
                <component :is="item.icon" class="size-4" />
                <span>{{ item.title }}</span>
              </NuxtLink>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroup>
    </SidebarContent>
    
    <SidebarFooter>
      <SidebarMenu>
        <SidebarMenuItem>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <Avatar class="h-8 w-8 rounded-lg">
                  <AvatarImage :src="user?.avatar_url" :alt="user?.first_name" />
                  <AvatarFallback class="rounded-lg">
                    {{ getInitials(user?.first_name, user?.last_name) }}
                  </AvatarFallback>
                </Avatar>
                <div class="grid flex-1 text-left text-sm leading-tight">
                  <span class="truncate font-semibold">{{ user?.first_name }} {{ user?.last_name }}</span>
                  <span class="truncate text-xs">{{ user?.email }}</span>
                </div>
                <ChevronsUpDown class="ml-auto size-4" />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              class="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              side="bottom"
              align="end"
              sideOffset="4"
            >
              <DropdownMenuLabel class="p-0 font-normal">
                <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                  <Avatar class="h-8 w-8 rounded-lg">
                    <AvatarImage :src="user?.avatar_url" :alt="user?.first_name" />
                    <AvatarFallback class="rounded-lg">
                      {{ getInitials(user?.first_name, user?.last_name) }}
                    </AvatarFallback>
                  </Avatar>
                  <div class="grid flex-1 text-left text-sm leading-tight">
                    <span class="truncate font-semibold">{{ user?.first_name }} {{ user?.last_name }}</span>
                    <span class="truncate text-xs">{{ user?.email }}</span>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  <Sparkles />
                  Upgrade to Pro
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  <BadgeCheck />
                  Account
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <CreditCard />
                  Billing
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Bell />
                  Notifications
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem @click="logout">
                <LogOut />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarFooter>
  </Sidebar>
</template>

<script setup lang="ts">
import { 
  BarChart3, 
  Building2, 
  Calendar, 
  Home, 
  MapPin, 
  Truck, 
  Users, 
  UserCheck,
  CreditCard,
  Settings,
  TrendingUp,
  DollarSign,
  Activity,
  Bell,
  BadgeCheck,
  ChevronsUpDown,
  LogOut,
  Sparkles
} from 'lucide-vue-next'
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import { useUserStore } from '~/stores/auth/user/user.store'
import { useAuthStore } from '~/stores/auth/auth.store'

// Get current user from stores
const userStore = useUserStore()
const authStore = useAuthStore()

const user = computed(() => userStore.currentUser)

const logout = async () => {
  authStore.logoutUser()
  await navigateTo('/auth/login')
}

// Navigation items
const mainNavItems = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: Home,
  },
  {
    title: "Bookings",
    url: "/dashboard/bookings",
    icon: Calendar,
    badge: "12"
  },
]

const operationsItems = [
  {
    title: "Routes",
    url: "/dashboard/routes",
    icon: MapPin,
  },
  {
    title: "Shuttles",
    url: "/dashboard/shuttles",
    icon: Truck,
  },
  {
    title: "Drivers",
    url: "/dashboard/drivers",
    icon: UserCheck,
  },
]

const managementItems = [
  {
    title: "Customers",
    url: "/dashboard/customers",
    icon: Users,
  },
  {
    title: "Partners",
    url: "/dashboard/partners",
    icon: Building2,
  },
]

const analyticsItems = [
  {
    title: "Analytics",
    url: "/dashboard/analytics",
    icon: BarChart3,
  },
  {
    title: "Revenue",
    url: "/dashboard/revenue",
    icon: DollarSign,
  },
  {
    title: "Performance",
    url: "/dashboard/performance",
    icon: TrendingUp,
  },
]

// Helper functions
const route = useRoute()

const isCurrentRoute = (url: string) => {
  if (url === '/dashboard') {
    return route.path === '/dashboard'
  }
  return route.path.startsWith(url)
}

const getInitials = (firstName?: string, lastName?: string) => {
  if (!firstName && !lastName) return 'U'
  return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase()
}
</script>
