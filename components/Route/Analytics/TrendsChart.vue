<template>
    <AnalyticsBaseTrendsChart
        title="Route Performance Analytics"
        subtitle="Track route demand, utilization, and revenue trends"
        :chart-types="chartTypes"
        :data="chartData"
        :is-loading="isLoading"
        :error="error"
        :summary-stats="summaryStats"
        :colors="['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6']"
        @chart-type-changed="onChartTypeChanged"
        @date-range-changed="onDateRangeChanged"
        @retry="fetchTrendData"
    />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { format, subDays } from 'date-fns';
import type { IRoute } from '~/stores/route/model/route.model';
import type { ChartType, SummaryStatConfig, ChartDataPoint } from '~/components/Analytics/BaseTrendsChart.vue';

const props = defineProps<{
    routes: IRoute[];
}>();

const isLoading = ref(false);
const error = ref<string | null>(null);
const currentChartType = ref('demand');

// Chart types
const chartTypes: ChartType[] = [
    { key: 'demand', label: 'Route Demand' },
    { key: 'utilization', label: 'Utilization Rate' },
    { key: 'revenue', label: 'Route Revenue' },
    { key: 'performance', label: 'On-Time Performance' }
];

// Generate mock trend data (in real app, fetch from API)
const generateTrendData = (type: string): ChartDataPoint[] => {
    return Array.from({ length: 30 }, (_, i) => {
        const date = format(subDays(new Date(), 29 - i), 'yyyy-MM-dd');
        let value = 0;
        
        switch (type) {
            case 'demand':
                value = Math.floor(Math.random() * 40) + 60; // 60-100% demand
                break;
            case 'utilization':
                value = Math.floor(Math.random() * 30) + 65; // 65-95% utilization
                break;
            case 'revenue':
                value = Math.floor(Math.random() * 3000) + 1500; // $1500-4500
                break;
            case 'performance':
                value = Math.floor(Math.random() * 15) + 85; // 85-100% on-time
                break;
        }
        
        return { x: date, y: value };
    });
};

const chartData = computed(() => generateTrendData(currentChartType.value));

// Summary statistics
const summaryStats = computed<SummaryStatConfig[]>(() => {
    const data = chartData.value;
    const values = data.map(d => d.y);
    
    const total = values.reduce((sum, val) => sum + val, 0);
    const average = values.length > 0 ? total / values.length : 0;
    const peak = values.length > 0 ? Math.max(...values) : 0;
    const growth = 8.7; // Mock growth
    
    let format: 'currency' | 'number' | 'percentage' = 'percentage';
    if (currentChartType.value === 'revenue') format = 'currency';
    
    const currentTypeLabel = chartTypes.find(t => t.key === currentChartType.value)?.label || 'Data';
    
    return [
        {
            key: 'average',
            label: `Avg ${currentTypeLabel}`,
            value: Math.round(average),
            format
        },
        {
            key: 'peak',
            label: 'Peak Performance',
            value: Math.round(peak),
            format
        },
        {
            key: 'total_routes',
            label: 'Total Routes',
            value: props.routes.length,
            format: 'number'
        },
        {
            key: 'growth',
            label: 'Growth Rate',
            value: growth,
            format: 'percentage'
        }
    ];
});

// Event handlers
const onChartTypeChanged = (type: string) => {
    currentChartType.value = type;
};

const onDateRangeChanged = (dates: { start: Date; end: Date }) => {
    // In real app, refetch data for new date range
    console.log('Date range changed:', dates);
    fetchTrendData();
};

const fetchTrendData = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        // Data is already computed reactively
    } catch (err) {
        error.value = 'Failed to load route trend data';
    } finally {
        isLoading.value = false;
    }
};

onMounted(() => {
    fetchTrendData();
});
</script>
