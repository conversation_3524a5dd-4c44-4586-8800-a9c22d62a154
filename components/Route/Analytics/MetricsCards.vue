<template>
  <!-- Show skeletons when loading -->
  <template v-if="status === 'pending'">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div v-for="i in 4" :key="i" class="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
        <div class="flex items-center justify-between">
          <div>
            <div class="h-3 w-20 bg-gray-200 rounded mb-2"></div>
            <div class="h-6 w-28 bg-gray-300 rounded mb-2"></div>
            <div class="h-3 w-16 bg-gray-200 rounded"></div>
          </div>
          <div class="p-3 rounded-full bg-gray-100">
            <div class="h-6 w-6 bg-gray-300 rounded-full"></div>
          </div>
        </div>
      </div>
    </div>
  </template>

  <!-- Actual metrics -->
  <template v-else>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Routes -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Routes</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalRoutes.toLocaleString() }}</p>
            <p class="text-xs text-gray-500 mt-1">
              <span :class="stats.growthVsLastMonth.totalRoutes >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ stats.growthVsLastMonth.totalRoutes >= 0 ? '+' : '' }}{{ stats.growthVsLastMonth.totalRoutes }}%
              </span>
              vs last month
            </p>
          </div>
          <div class="p-3 bg-blue-50 rounded-full">
            <MapPin class="h-6 w-6 text-blue-600" />
          </div>
        </div>
      </div>

      <!-- Active Routes -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Active Routes</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.activeRoutes.toLocaleString() }}</p>
            <p class="text-xs text-gray-500 mt-1">
              <span :class="stats.growthVsLastMonth.activeRoutes >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ stats.growthVsLastMonth.activeRoutes >= 0 ? '+' : '' }}{{ stats.growthVsLastMonth.activeRoutes }}%
              </span>
              vs last month
            </p>
          </div>
          <div class="p-3 bg-green-50 rounded-full">
            <Activity class="h-6 w-6 text-green-600" />
          </div>
        </div>
      </div>

      <!-- Average Route Price -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Average Route Price</p>
            <p class="text-2xl font-bold text-gray-900">GHC{{ stats.averageRoutePrice.toFixed(2) }}</p>
            <p class="text-xs text-gray-500 mt-1">
              <span :class="stats.growthVsLastMonth.averageRoutePrice >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ stats.growthVsLastMonth.averageRoutePrice >= 0 ? '+' : '' }}{{ stats.growthVsLastMonth.averageRoutePrice }}%
              </span>
              vs last month
            </p>
          </div>
          <div class="p-3 bg-emerald-50 rounded-full">
            <DollarSign class="h-6 w-6 text-emerald-600" />
          </div>
        </div>
      </div>

      <!-- Average Occupancy -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Average Occupancy</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.averageOccupancy.toFixed(1) }}%</p>
            <p class="text-xs text-gray-500 mt-1">
              <span :class="stats.growthVsLastMonth.averageOccupancy >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ stats.growthVsLastMonth.averageOccupancy >= 0 ? '+' : '' }}{{ stats.growthVsLastMonth.averageOccupancy }}%
              </span>
              vs last month
            </p>
          </div>
          <div class="p-3 bg-purple-50 rounded-full">
            <Users class="h-6 w-6 text-purple-600" />
          </div>
        </div>
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
import { MapPin, Activity, DollarSign, Users } from 'lucide-vue-next';
import type { IRouteStats } from '~/stores/route/interface/route.interface';
import { useRouteStore } from '~/stores/route/route.store';

const routeStore = useRouteStore();

// Fetch route stats using useAsyncData
const { data: stats, status, error, refresh } = await useAsyncData<IRouteStats>('route-stats', () => {
  return routeStore.getRouteStats();
}, {
  default: () => ({
    totalRoutes: 0,
    activeRoutes: 0,
    averageRoutePrice: 0,
    averageOccupancy: 0,
    growthVsLastMonth: {
      totalRoutes: 0,
      activeRoutes: 0,
      averageRoutePrice: 0,
      averageOccupancy: 0
    }
  } as IRouteStats),
  lazy: true,
});

// Refresh stats
const refreshStats = () => {
  routeStore.stats.data = {} as IRouteStats; // Clear data to show loading state
  refresh();
};
</script>