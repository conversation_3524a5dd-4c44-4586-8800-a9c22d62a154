<template>
    <AnalyticsBaseMetricsCards :metrics="routeMetrics" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    MapPin, 
    TrendingUp, 
    DollarSign, 
    Users, 
    Clock,
    Activity,
    Target,
    BarChart3
} from 'lucide-vue-next';
import type { IRoute } from '~/stores/route/model/route.model';
import type { MetricConfig } from '~/components/Analytics/BaseMetricsCards.vue';

const props = defineProps<{
    routes: IRoute[];
}>();

const routeMetrics = computed<MetricConfig[]>(() => {
    const totalRoutes = props.routes.length;
    const activeRoutes = props.routes.filter(r => r.status?.toLowerCase() === 'active').length;
    const totalRevenue = props.routes.reduce((sum, route) => sum + (route.base_price || 0), 0);
    const averagePrice = totalRoutes > 0 ? totalRevenue / totalRoutes : 0;
    
    // Calculate route efficiency (mock data - in real app, get from bookings/schedules)
    const routeEfficiency = 85.7;
    const demandScore = 78.3;
    const utilizationRate = 72.4;
    const onTimePerformance = 91.2;

    return [
        {
            key: 'total_routes',
            label: 'Total Routes',
            value: totalRoutes,
            format: 'number',
            trend: 6.8,
            icon: MapPin,
            iconBg: 'bg-blue-50',
            iconColor: 'text-blue-600'
        },
        {
            key: 'active_routes',
            label: 'Active Routes',
            value: activeRoutes,
            format: 'number',
            trend: 12.3,
            icon: Activity,
            iconBg: 'bg-green-50',
            iconColor: 'text-green-600'
        },
        {
            key: 'average_price',
            label: 'Average Route Price',
            value: averagePrice,
            format: 'currency',
            trend: 4.2,
            icon: DollarSign,
            iconBg: 'bg-emerald-50',
            iconColor: 'text-emerald-600'
        },
        {
            key: 'route_efficiency',
            label: 'Route Efficiency',
            value: routeEfficiency,
            format: 'percentage',
            trend: 2.8,
            icon: TrendingUp,
            iconBg: 'bg-purple-50',
            iconColor: 'text-purple-600'
        },
        {
            key: 'demand_score',
            label: 'Demand Score',
            value: demandScore,
            format: 'percentage',
            trend: 8.1,
            trendLabel: 'vs last month',
            icon: BarChart3,
            iconBg: 'bg-orange-50',
            iconColor: 'text-orange-600'
        },
        {
            key: 'utilization_rate',
            label: 'Utilization Rate',
            value: utilizationRate,
            format: 'percentage',
            trend: 5.4,
            icon: Users,
            iconBg: 'bg-indigo-50',
            iconColor: 'text-indigo-600'
        },
        {
            key: 'on_time_performance',
            label: 'On-Time Performance',
            value: onTimePerformance,
            format: 'percentage',
            trend: 3.7,
            icon: Clock,
            iconBg: 'bg-teal-50',
            iconColor: 'text-teal-600'
        },
        {
            key: 'revenue_per_route',
            label: 'Revenue per Route',
            value: averagePrice * 25, // Mock multiplier for monthly revenue
            format: 'currency',
            trend: 7.9,
            trendLabel: 'monthly avg',
            icon: Target,
            iconBg: 'bg-rose-50',
            iconColor: 'text-rose-600'
        }
    ];
});
</script>
