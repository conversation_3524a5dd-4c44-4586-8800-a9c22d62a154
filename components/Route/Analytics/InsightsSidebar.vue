<template>
    <AnalyticsBaseInsightsSidebar
        title="Route Insights"
        subtitle="Route performance analytics and optimization insights"
        :is-sheet="isSheet"
        :today-stats="todayStats"
        :alerts="alerts"
        :recent-activity="recentActivity"
        :quick-actions="quickActions"
        :performance-metrics="performanceMetrics"
        @close="$emit('close')"
        @quick-action="handleQuickAction"
    />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
    AlertTriangle, 
    Info, 
    Plus, 
    Minus, 
    Settings,
    Download,
    FileText,
    MapPin,
    TrendingUp,
    Clock,
    DollarSign
} from 'lucide-vue-next';
import type { IRoute } from '~/stores/route/model/route.model';
import type {
    TodayStatConfig,
    AlertConfig,
    ActivityConfig,
    QuickActionConfig,
    PerformanceMetricConfig
} from '~/components/Analytics/BaseInsightsSidebar.vue';

const props = defineProps<{
    routes: IRoute[];
    isSheet?: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
}>();

// Today's stats
const todayStats = computed<TodayStatConfig[]>(() => {
    const totalRoutes = props.routes.length;
    const activeRoutes = props.routes.filter(r => r.status?.toLowerCase() === 'active').length;
    const totalRevenue = props.routes.reduce((sum, route) => sum + (route.base_price || 0), 0);
    const averagePrice = totalRoutes > 0 ? totalRevenue / totalRoutes : 0;
    
    return [
        {
            key: 'active_routes',
            label: 'Active Routes',
            value: activeRoutes,
            color: 'bg-green-500'
        },
        {
            key: 'total_routes',
            label: 'Total Routes',
            value: totalRoutes,
            color: 'bg-blue-500'
        },
        {
            key: 'avg_price',
            label: 'Avg Route Price',
            value: averagePrice,
            format: 'currency',
            color: 'bg-purple-500'
        },
        {
            key: 'efficiency',
            label: 'Route Efficiency',
            value: 85.7,
            format: 'percentage',
            color: 'bg-orange-500'
        }
    ];
});

// Alerts
const alerts = computed<AlertConfig[]>(() => {
    const alertsList: AlertConfig[] = [];
    
    const lowDemandRoutes = props.routes.filter(r => {
        // Mock low demand detection based on price (lower price might indicate lower demand)
        return (r.base_price || 0) < 50;
    });
    
    if (lowDemandRoutes.length > 0) {
        alertsList.push({
            id: 'low_demand',
            type: 'warning',
            title: 'Low Demand Routes',
            message: `${lowDemandRoutes.length} route${lowDemandRoutes.length > 1 ? 's' : ''} showing low demand patterns`,
            time: '45 minutes ago',
            icon: AlertTriangle
        });
    }
    
    const highPriceRoutes = props.routes.filter(r => (r.base_price || 0) > 200);
    if (highPriceRoutes.length > 0) {
        alertsList.push({
            id: 'pricing_optimization',
            type: 'info',
            title: 'Pricing Optimization',
            message: `${highPriceRoutes.length} premium route${highPriceRoutes.length > 1 ? 's' : ''} available for demand analysis`,
            time: '2 hours ago',
            icon: Info
        });
    }
    
    return alertsList;
});

// Recent activity
const recentActivity = computed<ActivityConfig[]>(() => [
    {
        id: 1,
        message: 'Route "City Express" demand increased by 15%',
        time: '5 min ago',
        icon: TrendingUp,
        iconColor: 'bg-green-100 text-green-600'
    },
    {
        id: 2,
        message: 'New route "Airport Shuttle" created',
        time: '18 min ago',
        icon: Plus,
        iconColor: 'bg-blue-100 text-blue-600'
    },
    {
        id: 3,
        message: 'Route pricing updated for "Downtown Loop"',
        time: '32 min ago',
        icon: DollarSign,
        iconColor: 'bg-purple-100 text-purple-600'
    },
    {
        id: 4,
        message: 'Route "Suburban Connect" on-time performance: 94%',
        time: '1 hour ago',
        icon: Clock,
        iconColor: 'bg-teal-100 text-teal-600'
    }
]);

// Quick actions
const quickActions = computed<QuickActionConfig[]>(() => [
    {
        key: 'export_routes',
        label: 'Export Route Data',
        icon: Download
    },
    {
        key: 'route_report',
        label: 'Generate Route Report',
        icon: FileText
    },
    {
        key: 'optimize_pricing',
        label: 'Optimize Pricing',
        icon: DollarSign
    },
    {
        key: 'route_settings',
        label: 'Route Settings',
        icon: Settings
    }
]);

// Performance metrics
const performanceMetrics = computed<PerformanceMetricConfig[]>(() => [
    {
        key: 'demand_score',
        label: 'Overall Demand Score',
        value: 78.3,
        format: 'percentage',
        color: 'bg-green-500',
        percentage: 78.3
    },
    {
        key: 'utilization_rate',
        label: 'Route Utilization',
        value: 72.4,
        format: 'percentage',
        color: 'bg-blue-500',
        percentage: 72.4
    },
    {
        key: 'on_time_performance',
        label: 'On-Time Performance',
        value: 91.2,
        format: 'percentage',
        color: 'bg-purple-500',
        percentage: 91.2
    },
    {
        key: 'revenue_efficiency',
        label: 'Revenue Efficiency',
        value: 85.7,
        format: 'percentage',
        color: 'bg-orange-500',
        percentage: 85.7
    }
]);

// Handle quick actions
const handleQuickAction = (actionKey: string) => {
    switch (actionKey) {
        case 'export_routes':
            console.log('Exporting route data...');
            break;
        case 'route_report':
            console.log('Generating route report...');
            break;
        case 'optimize_pricing':
            console.log('Opening pricing optimization...');
            break;
        case 'route_settings':
            console.log('Opening route settings...');
            break;
    }
};
</script>
