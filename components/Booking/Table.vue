<template>
    <div>
        <DataTable 
            :columns="columns" 
            :data="bookings" 
            :is-loading="isLoading"
            @get-table-data="handleDataTableData" 
            @get-row-data="handleRowClicked"
        >
            <template #dataTableSearch>
                <Input 
                    v-if="dataTableRef" 
                    class="w-1/3" 
                    type="search" 
                    placeholder="Search..."
                    :model-value="(dataTableRef.getColumn('booking_reference')?.getFilterValue() as string) ?? ''"
                    @input="dataTableRef.getColumn('booking_reference')?.setFilterValue($event.target.value)" 
                />
            </template>
        </DataTable>
    </div>
</template>

<script setup lang="ts">
import type { ColumnDef, Row, Table } from '@tanstack/vue-table'
import { Checkbox } from '@/components/ui/checkbox'
import UserFullAvatar from '@/components/UserFullAvatar.vue'
import { Badge } from '@/components/ui/badge';
import type { IBooking } from '~/stores/booking/model/booking.model';

const props = defineProps<{
    bookings: IBooking[];
    isLoading?: boolean;
}>();

const emit = defineEmits<{
    (e: 'rowClick', row: Row<IBooking>): void;
}>();

// DATA TABLE STUFF
const dataTableRef = ref<Table<IBooking>>();

const handleDataTableData = (data: Table<IBooking>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<IBooking>) => {
    emit('rowClick', row);
};

const columns: ColumnDef<IBooking>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllPageRowsSelected(),
            'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => row.toggleSelected(!!value),
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'booking_reference',
        header: 'Reference',
        cell: ({ row }) => h('div', { class: 'font-mono text-sm' }, row.original.booking_reference),
    },
    {
        accessorKey: 'booking_schedule_date',
        header: 'Trip Date',
        cell: ({ row }) => h('div', { class: 'text-sm' }, [
            h('div', { class: 'font-medium' }, useFormatDateHuman(new Date(row.original.booking_schedule_date))),
            h('div', { class: 'text-xs text-gray-500' }, `${row.original.departure_time} - ${row.original.arrival_time}`)
        ]),
    },
    {
        header: 'Route',
        cell: ({ row }) => h('div', { class: 'text-sm' }, [
            h('div', { class: 'font-medium' }, `${row.original.route.origin.name} → ${row.original.route.destination.name}`),
            h('div', { class: 'text-xs text-gray-500 flex items-center space-x-1' }, [
                h('span', {}, `${row.original.pickup_stop.name || 'Standard pickup'}`),
                h('span', {}, '→'),
                h('span', {}, `${row.original.drop_off_stop.name || 'Standard drop-off'}`)
            ])
        ]),
    },
    {
        header: 'Rider',
        cell: ({ row }) => h(UserFullAvatar, {
            firstname: row.original.rider.first_name ?? '',
            lastname: row.original.rider.last_name ?? '',
            avatarUrl: row.original.rider.avatar_url ?? '',
        }),
    },
    {
        header: 'Booking Details',
        cell: ({ row }) => h('div', { class: 'text-sm' }, [
            h('div', { class: 'font-medium' }, `${row.original.number_of_tickets} ticket${row.original.number_of_tickets > 1 ? 's' : ''}`),
            h('div', { class: 'text-xs text-gray-500' }, `$${(row.original.total_amount / row.original.number_of_tickets).toFixed(2)} per ticket`)
        ]),
    },
    {
        accessorKey: 'total_amount',
        header: 'Revenue',
        cell: ({ row }) => h('div', { class: 'text-sm font-semibold' }, [
            h('div', { class: 'text-green-600' }, `$${row.original.total_amount.toFixed(2)}`),
            h('div', { class: 'text-xs text-gray-500' }, getRevenueCategory(row.original.total_amount))
        ]),
    },
    {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => h('div', { class: 'flex flex-col space-y-1' }, [
            h(Badge, {
                variant: 'secondary',
                class: getStatusColor(row.original.status)
            }, row.original.status),
            h('div', { class: 'text-xs text-gray-500' }, getStatusDescription(row.original.status))
        ]),
    },
    {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => h('div', { class: 'flex space-x-2' }, [
            h('button', {
                class: 'px-2 py-1 text-xs text-blue-600 hover:text-blue-800 hover:underline',
                onClick: (e: Event) => {
                    e.stopPropagation();
                    emit('rowClick', row);
                }
            }, 'View'),
            h('button', {
                class: 'px-2 py-1 text-xs text-gray-600 hover:text-gray-800 hover:underline',
                onClick: (e: Event) => {
                    e.stopPropagation();
                    // Handle quick actions like refund, cancel, etc.
                }
            }, 'Actions')
        ])
    }
];

// Helper functions for enhanced display
const getStatusColor = (status: string) => {
    const statusColors = {
        'confirmed': 'bg-green-100 text-green-800',
        'success': 'bg-green-100 text-green-800',
        'completed': 'bg-blue-100 text-blue-800',
        'pending': 'bg-yellow-100 text-yellow-800',
        'cancelled': 'bg-red-100 text-red-800',
        'refunded': 'bg-gray-100 text-gray-800'
    };
    return statusColors[status.toLowerCase() as keyof typeof statusColors] || 'bg-gray-100 text-gray-800';
};

const getStatusDescription = (status: string) => {
    const descriptions = {
        'confirmed': 'Trip confirmed',
        'success': 'Payment successful',
        'completed': 'Trip completed',
        'pending': 'Awaiting payment',
        'cancelled': 'Booking cancelled',
        'refunded': 'Amount refunded'
    };
    return descriptions[status.toLowerCase() as keyof typeof descriptions] || 'Status unknown';
};

const getRevenueCategory = (amount: number) => {
    if (amount >= 100) return 'High value';
    if (amount >= 50) return 'Medium value';
    return 'Standard';
};
</script>
