<template>
    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
            <div class="flex items-center space-x-2">
                <Button variant="outline" size="sm" @click="clearAllFilters">
                    Clear All
                </Button>
                <Button 
                    variant="ghost" 
                    size="sm" 
                    @click="isExpanded = !isExpanded"
                    class="text-gray-500"
                >
                    <ChevronDown :class="{ 'rotate-180': isExpanded }" class="h-4 w-4 transition-transform" />
                </Button>
            </div>
        </div>

        <!-- Quick Filters (Always Visible) -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <!-- Search -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <Input 
                    v-model="filters.search"
                    placeholder="Reference, rider name..."
                    class="w-full"
                    @input="onFiltersChange"
                />
            </div>

            <!-- Status Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <Select v-model="filters.status" @update:model-value="onFiltersChange">
                    <SelectTrigger>
                        <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="">All statuses</SelectItem>
                        <SelectItem value="confirmed">Confirmed</SelectItem>
                        <SelectItem value="success">Success</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                        <SelectItem value="refunded">Refunded</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <!-- Date Range -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                <DateRangePicker @handle-date-change="onDateRangeChange" />
            </div>

            <!-- Quick Revenue Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Revenue</label>
                <Select v-model="filters.revenueRange" @update:model-value="onFiltersChange">
                    <SelectTrigger>
                        <SelectValue placeholder="All amounts" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="">All amounts</SelectItem>
                        <SelectItem value="0-25">$0 - $25</SelectItem>
                        <SelectItem value="25-50">$25 - $50</SelectItem>
                        <SelectItem value="50-100">$50 - $100</SelectItem>
                        <SelectItem value="100+">$100+</SelectItem>
                    </SelectContent>
                </Select>
            </div>
        </div>

        <!-- Advanced Filters (Collapsible) -->
        <Collapsible v-model:open="isExpanded">
            <CollapsibleContent>
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                        <!-- Route Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Route</label>
                            <Select v-model="filters.route" @update:model-value="onFiltersChange">
                                <SelectTrigger>
                                    <SelectValue placeholder="All routes" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All routes</SelectItem>
                                    <SelectItem v-for="route in availableRoutes" :key="route" :value="route">
                                        {{ route }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <!-- Tickets Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Tickets</label>
                            <Select v-model="filters.ticketCount" @update:model-value="onFiltersChange">
                                <SelectTrigger>
                                    <SelectValue placeholder="Any count" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">Any count</SelectItem>
                                    <SelectItem value="1">1 ticket</SelectItem>
                                    <SelectItem value="2">2 tickets</SelectItem>
                                    <SelectItem value="3">3 tickets</SelectItem>
                                    <SelectItem value="4+">4+ tickets</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Payment</label>
                            <Select v-model="filters.paymentMethod" @update:model-value="onFiltersChange">
                                <SelectTrigger>
                                    <SelectValue placeholder="All methods" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All methods</SelectItem>
                                    <SelectItem value="card">Credit Card</SelectItem>
                                    <SelectItem value="cash">Cash</SelectItem>
                                    <SelectItem value="mobile">Mobile Payment</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <!-- Booking Source -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Source</label>
                            <Select v-model="filters.source" @update:model-value="onFiltersChange">
                                <SelectTrigger>
                                    <SelectValue placeholder="All sources" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All sources</SelectItem>
                                    <SelectItem value="app">Mobile App</SelectItem>
                                    <SelectItem value="web">Website</SelectItem>
                                    <SelectItem value="admin">Admin Panel</SelectItem>
                                    <SelectItem value="partner">Partner</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <!-- Sort By -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                            <Select v-model="filters.sortBy" @update:model-value="onFiltersChange">
                                <SelectTrigger>
                                    <SelectValue placeholder="Date (newest)" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="date_desc">Date (newest)</SelectItem>
                                    <SelectItem value="date_asc">Date (oldest)</SelectItem>
                                    <SelectItem value="amount_desc">Amount (highest)</SelectItem>
                                    <SelectItem value="amount_asc">Amount (lowest)</SelectItem>
                                    <SelectItem value="status">Status</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <!-- Custom Revenue Range -->
                    <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Custom Revenue Range</label>
                            <div class="flex items-center space-x-2">
                                <Input 
                                    v-model="filters.minAmount"
                                    type="number"
                                    placeholder="Min $"
                                    class="w-full"
                                    @input="onFiltersChange"
                                />
                                <span class="text-gray-500">to</span>
                                <Input 
                                    v-model="filters.maxAmount"
                                    type="number"
                                    placeholder="Max $"
                                    class="w-full"
                                    @input="onFiltersChange"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </CollapsibleContent>
        </Collapsible>

        <!-- Active Filters Display -->
        <div v-if="activeFiltersCount > 0" class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-700">Active filters:</span>
                    <Badge variant="secondary">{{ activeFiltersCount }}</Badge>
                </div>
                <div class="flex flex-wrap gap-2">
                    <Badge 
                        v-for="filter in activeFiltersList" 
                        :key="filter.key"
                        variant="outline"
                        class="cursor-pointer hover:bg-red-50 hover:border-red-200"
                        @click="removeFilter(filter.key)"
                    >
                        {{ filter.label }}
                        <X class="h-3 w-3 ml-1" />
                    </Badge>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue';
import { ChevronDown, X } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import { 
    Collapsible, 
    CollapsibleContent 
} from '@/components/ui/collapsible';
import type { IBooking } from '~/stores/booking/model/booking.model';

const props = defineProps<{
    bookings: IBooking[];
}>();

const emit = defineEmits<{
    (e: 'filtersChanged', filters: any): void;
}>();

const isExpanded = ref(false);

// Filter state
const filters = reactive({
    search: '',
    status: '',
    dateRange: null as { start: Date; end: Date } | null,
    revenueRange: '',
    route: '',
    ticketCount: '',
    paymentMethod: '',
    source: '',
    sortBy: 'date_desc',
    minAmount: '',
    maxAmount: ''
});

// Available routes (computed from bookings)
const availableRoutes = computed(() => {
    const routes = new Set<string>();
    props.bookings.forEach(booking => {
        const routeName = `${booking.route.origin.name} → ${booking.route.destination.name}`;
        routes.add(routeName);
    });
    return Array.from(routes).sort();
});

// Active filters count
const activeFiltersCount = computed(() => {
    let count = 0;
    if (filters.search) count++;
    if (filters.status) count++;
    if (filters.dateRange) count++;
    if (filters.revenueRange) count++;
    if (filters.route) count++;
    if (filters.ticketCount) count++;
    if (filters.paymentMethod) count++;
    if (filters.source) count++;
    if (filters.minAmount || filters.maxAmount) count++;
    return count;
});

// Active filters list for display
const activeFiltersList = computed(() => {
    const list = [];
    if (filters.search) list.push({ key: 'search', label: `Search: ${filters.search}` });
    if (filters.status) list.push({ key: 'status', label: `Status: ${filters.status}` });
    if (filters.dateRange) list.push({ key: 'dateRange', label: 'Date range selected' });
    if (filters.revenueRange) list.push({ key: 'revenueRange', label: `Revenue: ${filters.revenueRange}` });
    if (filters.route) list.push({ key: 'route', label: `Route: ${filters.route}` });
    if (filters.ticketCount) list.push({ key: 'ticketCount', label: `Tickets: ${filters.ticketCount}` });
    if (filters.paymentMethod) list.push({ key: 'paymentMethod', label: `Payment: ${filters.paymentMethod}` });
    if (filters.source) list.push({ key: 'source', label: `Source: ${filters.source}` });
    if (filters.minAmount || filters.maxAmount) {
        const range = `$${filters.minAmount || '0'} - $${filters.maxAmount || '∞'}`;
        list.push({ key: 'customAmount', label: `Amount: ${range}` });
    }
    return list;
});

// Handle filter changes
const onFiltersChange = () => {
    emit('filtersChanged', { ...filters });
};

// Handle date range changes
const onDateRangeChange = (dates: { start: Date; end: Date }) => {
    filters.dateRange = dates;
    onFiltersChange();
};

// Remove specific filter
const removeFilter = (key: string) => {
    switch (key) {
        case 'search':
            filters.search = '';
            break;
        case 'status':
            filters.status = '';
            break;
        case 'dateRange':
            filters.dateRange = null;
            break;
        case 'revenueRange':
            filters.revenueRange = '';
            break;
        case 'route':
            filters.route = '';
            break;
        case 'ticketCount':
            filters.ticketCount = '';
            break;
        case 'paymentMethod':
            filters.paymentMethod = '';
            break;
        case 'source':
            filters.source = '';
            break;
        case 'customAmount':
            filters.minAmount = '';
            filters.maxAmount = '';
            break;
    }
    onFiltersChange();
};

// Clear all filters
const clearAllFilters = () => {
    Object.keys(filters).forEach(key => {
        if (key === 'dateRange') {
            filters[key as keyof typeof filters] = null;
        } else if (key === 'sortBy') {
            filters[key as keyof typeof filters] = 'date_desc';
        } else {
            filters[key as keyof typeof filters] = '';
        }
    });
    onFiltersChange();
};
</script>
