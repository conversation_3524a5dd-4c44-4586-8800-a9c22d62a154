<template>
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Booking Trends</h3>
                <p class="text-sm text-gray-500">Track booking patterns and revenue over time</p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Chart Type Toggle -->
                <div class="flex bg-gray-100 rounded-lg p-1">
                    <button 
                        @click="chartType = 'bookings'"
                        :class="[
                            'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                            chartType === 'bookings' 
                                ? 'bg-white text-gray-900 shadow-sm' 
                                : 'text-gray-600 hover:text-gray-900'
                        ]"
                    >
                        Bookings
                    </button>
                    <button 
                        @click="chartType = 'revenue'"
                        :class="[
                            'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                            chartType === 'revenue' 
                                ? 'bg-white text-gray-900 shadow-sm' 
                                : 'text-gray-600 hover:text-gray-900'
                        ]"
                    >
                        Revenue
                    </button>
                </div>
                
                <!-- Date Range Picker -->
                <DateRangePicker @handle-date-change="onDateChanged" />
            </div>
        </div>

        <div v-if="isLoading" class="w-full h-80 flex items-center justify-center">
            <Loader2 class="h-8 w-8 animate-spin text-primary" />
        </div>
        
        <div v-else-if="error" class="w-full h-80 flex items-center justify-center">
            <div class="text-center">
                <p class="text-red-500">{{ error }}</p>
                <Button variant="outline" size="sm" class="mt-2" @click="fetchTrendData">Retry</Button>
            </div>
        </div>
        
        <template v-else>
            <ClientOnly>
                <apexchart 
                    :key="chartOptions.series" 
                    height="320" 
                    width="100%" 
                    :options="chartOptions"
                    :series="chartOptions.series"
                />
            </ClientOnly>
        </template>

        <!-- Summary Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-gray-200">
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-900">{{ summaryStats.total.toLocaleString() }}</p>
                <p class="text-sm text-gray-500">Total {{ chartType === 'bookings' ? 'Bookings' : 'Revenue' }}</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-900">{{ summaryStats.average.toLocaleString() }}</p>
                <p class="text-sm text-gray-500">Daily Average</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-900">{{ summaryStats.peak.toLocaleString() }}</p>
                <p class="text-sm text-gray-500">Peak Day</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold" :class="summaryStats.growth >= 0 ? 'text-green-600' : 'text-red-600'">
                    {{ summaryStats.growth >= 0 ? '+' : '' }}{{ summaryStats.growth }}%
                </p>
                <p class="text-sm text-gray-500">Growth</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Loader2 } from 'lucide-vue-next';
import { format, subDays, parseISO } from 'date-fns';
import type { IBooking } from '~/stores/booking/model/booking.model';

const props = defineProps<{
    bookings: IBooking[];
}>();

const chartType = ref<'bookings' | 'revenue'>('bookings');
const isLoading = ref(false);
const error = ref<string | null>(null);

// Date range for filtering
const startDate = ref(format(subDays(new Date(), 30), 'yyyy-MM-dd'));
const endDate = ref(format(new Date(), 'yyyy-MM-dd'));

// Process chart data based on bookings
const chartData = computed(() => {
    const filteredBookings = props.bookings.filter(booking => {
        const bookingDate = new Date(booking.booking_schedule_date);
        const start = new Date(startDate.value);
        const end = new Date(endDate.value);
        return bookingDate >= start && bookingDate <= end;
    });

    // Group bookings by date
    const groupedData = filteredBookings.reduce((acc, booking) => {
        const date = format(new Date(booking.booking_schedule_date), 'yyyy-MM-dd');
        if (!acc[date]) {
            acc[date] = { bookings: 0, revenue: 0 };
        }
        acc[date].bookings += 1;
        acc[date].revenue += booking.total_amount;
        return acc;
    }, {} as Record<string, { bookings: number; revenue: number }>);

    // Convert to chart format
    return Object.entries(groupedData)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([date, data]) => ({
            x: date,
            y: chartType.value === 'bookings' ? data.bookings : data.revenue
        }));
});

// Chart options
const chartOptions = computed(() => ({
    chart: {
        type: 'area',
        height: 320,
        toolbar: {
            show: false,
        },
        zoom: {
            enabled: false,
        },
    },
    series: [{
        name: chartType.value === 'bookings' ? 'Bookings' : 'Revenue ($)',
        data: chartData.value
    }],
    dataLabels: {
        enabled: false,
    },
    stroke: {
        show: true,
        curve: 'smooth',
        lineCap: 'butt',
        width: 3,
    },
    xaxis: {
        type: 'datetime',
        labels: {
            format: 'dd MMM',
        }
    },
    yaxis: {
        show: true,
        title: {
            text: chartType.value === 'bookings' ? 'Number of Bookings' : 'Revenue ($)',
        },
        min: 0,
        forceNiceScale: true,
        labels: {
            formatter: (value: number) => {
                if (chartType.value === 'revenue') {
                    return 'GHC' + value.toLocaleString();
                }
                return value.toString();
            }
        }
    },
    tooltip: {
        enabled: true,
        x: {
            format: 'dd MMM yyyy'
        },
        y: {
            formatter: (value: number) => {
                if (chartType.value === 'revenue') {
                    return 'GHC' + value.toLocaleString();
                }
                return value + ' bookings';
            }
        }
    },
    fill: {
        opacity: 0.3,
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: undefined,
            inverseColors: false,
            opacityFrom: 0.5,
            opacityTo: 0.1,
        }
    },
    colors: [chartType.value === 'bookings' ? '#3B82F6' : '#10B981'],
    grid: {
        show: true,
        borderColor: '#E5E7EB',
        strokeDashArray: 0,
        position: 'back',
        xaxis: {
            lines: {
                show: false
            }
        },
        yaxis: {
            lines: {
                show: true
            }
        }
    },
    legend: {
        show: false
    },
}));

// Summary statistics
const summaryStats = computed(() => {
    const data = chartData.value;
    const values = data.map(d => d.y);
    
    const total = values.reduce((sum, val) => sum + val, 0);
    const average = values.length > 0 ? total / values.length : 0;
    const peak = values.length > 0 ? Math.max(...values) : 0;
    
    // Mock growth calculation (in real app, compare with previous period)
    const growth = chartType.value === 'bookings' ? 12.5 : 8.3;
    
    return {
        total: Math.round(total),
        average: Math.round(average),
        peak: Math.round(peak),
        growth: growth
    };
});

// Handle date range changes
const onDateChanged = (dates: { start: Date; end: Date }) => {
    startDate.value = format(dates.start, 'yyyy-MM-dd');
    endDate.value = format(dates.end, 'yyyy-MM-dd');
};

// Mock fetch function (in real app, this would fetch from API)
const fetchTrendData = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        // Data is already computed from props.bookings
    } catch (err) {
        error.value = 'Failed to load trend data';
    } finally {
        isLoading.value = false;
    }
};

onMounted(() => {
    fetchTrendData();
});
</script>
