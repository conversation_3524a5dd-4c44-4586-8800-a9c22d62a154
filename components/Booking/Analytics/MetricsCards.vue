<template>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Total Bookings -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Bookings</p>
                    <p class="text-2xl font-bold text-gray-900">{{ totalBookings.toLocaleString() }}</p>
                    <p class="text-xs text-gray-500 mt-1">
                        <span :class="bookingsTrend >= 0 ? 'text-green-600' : 'text-red-600'">
                            {{ bookingsTrend >= 0 ? '+' : '' }}{{ bookingsTrend }}%
                        </span>
                        vs last month
                    </p>
                </div>
                <div class="p-3 bg-blue-50 rounded-full">
                    <Calendar class="h-6 w-6 text-blue-600" />
                </div>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p class="text-2xl font-bold text-gray-900">GHC{{ totalRevenue.toLocaleString() }}</p>
                    <p class="text-xs text-gray-500 mt-1">
                        <span :class="revenueTrend >= 0 ? 'text-green-600' : 'text-red-600'">
                            {{ revenueTrend >= 0 ? '+' : '' }}{{ revenueTrend }}%
                        </span>
                        vs last month
                    </p>
                </div>
                <div class="p-3 bg-green-50 rounded-full">
                    <DollarSign class="h-6 w-6 text-green-600" />
                </div>
            </div>
        </div>

        <!-- Average Booking Value -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Avg. Booking Value</p>
                    <p class="text-2xl font-bold text-gray-900">${{ averageBookingValue.toFixed(2) }}</p>
                    <p class="text-xs text-gray-500 mt-1">
                        <span :class="avgValueTrend >= 0 ? 'text-green-600' : 'text-red-600'">
                            {{ avgValueTrend >= 0 ? '+' : '' }}{{ avgValueTrend }}%
                        </span>
                        vs last month
                    </p>
                </div>
                <div class="p-3 bg-purple-50 rounded-full">
                    <TrendingUp class="h-6 w-6 text-purple-600" />
                </div>
            </div>
        </div>

        <!-- Completion Rate -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Completion Rate</p>
                    <p class="text-2xl font-bold text-gray-900">{{ completionRate.toFixed(1) }}%</p>
                    <p class="text-xs text-gray-500 mt-1">
                        <span :class="completionTrend >= 0 ? 'text-green-600' : 'text-red-600'">
                            {{ completionTrend >= 0 ? '+' : '' }}{{ completionTrend }}%
                        </span>
                        vs last month
                    </p>
                </div>
                <div class="p-3 bg-orange-50 rounded-full">
                    <CheckCircle class="h-6 w-6 text-orange-600" />
                </div>
            </div>
        </div>
    </div>

    <!-- Status Distribution -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Status Breakdown -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Booking Status</h3>
            <div class="space-y-3">
                <div v-for="status in statusBreakdown" :key="status.name" class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div :class="status.color" class="w-3 h-3 rounded-full"></div>
                        <span class="text-sm font-medium text-gray-700">{{ status.name }}</span>
                    </div>
                    <div class="text-right">
                        <span class="text-sm font-bold text-gray-900">{{ status.count }}</span>
                        <span class="text-xs text-gray-500 ml-1">({{ status.percentage }}%)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Routes -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Popular Routes</h3>
            <div class="space-y-3">
                <div v-for="route in popularRoutes" :key="route.name" class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-900">{{ route.name }}</p>
                        <p class="text-xs text-gray-500">{{ route.origin }} → {{ route.destination }}</p>
                    </div>
                    <div class="text-right">
                        <span class="text-sm font-bold text-gray-900">{{ route.bookings }}</span>
                        <span class="text-xs text-gray-500 block">${{ route.revenue.toLocaleString() }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div class="space-y-3">
                <div v-for="activity in recentActivity" :key="activity.id" class="flex items-start space-x-3">
                    <div :class="activity.iconColor" class="p-1 rounded-full">
                        <component :is="activity.icon" class="h-3 w-3" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-xs text-gray-900">{{ activity.message }}</p>
                        <p class="text-xs text-gray-500">{{ activity.time }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Calendar, DollarSign, TrendingUp, CheckCircle, Plus, Minus, AlertCircle } from 'lucide-vue-next';
import type { IBooking } from '~/stores/booking/model/booking.model';

const props = defineProps<{
    bookings: IBooking[];
}>();

// Calculate metrics
const totalBookings = computed(() => props.bookings.length);

const totalRevenue = computed(() => 
    props.bookings.reduce((sum, booking) => sum + booking.total_amount, 0)
);

const averageBookingValue = computed(() => 
    totalBookings.value > 0 ? totalRevenue.value / totalBookings.value : 0
);

const completionRate = computed(() => {
    const completedBookings = props.bookings.filter(b => 
        b.status.toLowerCase() === 'completed' || b.status.toLowerCase() === 'success'
    ).length;
    return totalBookings.value > 0 ? (completedBookings / totalBookings.value) * 100 : 0;
});

// Mock trend data (in real app, this would come from API)
const bookingsTrend = ref(12.5);
const revenueTrend = ref(8.3);
const avgValueTrend = ref(-2.1);
const completionTrend = ref(5.7);

// Status breakdown
const statusBreakdown = computed(() => {
    const statusCounts = props.bookings.reduce((acc, booking) => {
        const status = booking.status.toLowerCase();
        acc[status] = (acc[status] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    const statusColors = {
        'confirmed': 'bg-green-500',
        'success': 'bg-green-500',
        'completed': 'bg-blue-500',
        'pending': 'bg-yellow-500',
        'cancelled': 'bg-red-500',
        'refunded': 'bg-gray-500'
    };

    return Object.entries(statusCounts).map(([status, count]) => ({
        name: status.charAt(0).toUpperCase() + status.slice(1),
        count,
        percentage: ((count / totalBookings.value) * 100).toFixed(1),
        color: statusColors[status as keyof typeof statusColors] || 'bg-gray-500'
    }));
});

// Popular routes (mock data - in real app, calculate from bookings)
const popularRoutes = ref([
    { name: 'City Express', origin: 'Downtown', destination: 'Airport', bookings: 45, revenue: 2250 },
    { name: 'Campus Shuttle', origin: 'University', destination: 'Mall', bookings: 38, revenue: 1520 },
    { name: 'Business Route', origin: 'CBD', destination: 'Tech Park', bookings: 32, revenue: 1920 }
]);

// Recent activity (mock data)
const recentActivity = ref([
    { id: 1, message: 'New booking #BK001234', time: '2 min ago', icon: Plus, iconColor: 'bg-green-100 text-green-600' },
    { id: 2, message: 'Booking #BK001230 cancelled', time: '5 min ago', icon: Minus, iconColor: 'bg-red-100 text-red-600' },
    { id: 3, message: 'Payment issue #BK001228', time: '12 min ago', icon: AlertCircle, iconColor: 'bg-yellow-100 text-yellow-600' }
]);
</script>
