<template>
    <!-- Show skeletons when loading -->
    <template v-if="status == 'pending'">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div v-for="i in 4" :key="i" class="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="h-3 w-20 bg-gray-200 rounded mb-2"></div>
                        <div class="h-6 w-28 bg-gray-300 rounded mb-2"></div>
                        <div class="h-3 w-16 bg-gray-200 rounded"></div>
                    </div>
                    <div class="p-3 rounded-full bg-gray-100">
                        <div class="h-6 w-6 bg-gray-300 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Actual metrics -->
    <template v-else>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Total Bookings -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Bookings</p>
                        <p class="text-2xl font-bold text-gray-900">{{ stats.totalBookings.toLocaleString() }}</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <span
                                :class="stats.growthVsLastMonth.totalBookings >= 0 ? 'text-green-600' : 'text-red-600'">
                                {{ stats.growthVsLastMonth.totalBookings >= 0 ? '+' : '' }}{{
                                    stats.growthVsLastMonth.totalBookings }}%
                            </span>
                            vs last month
                        </p>
                    </div>
                    <div class="p-3 bg-blue-50 rounded-full">
                        <Calendar class="h-6 w-6 text-blue-600" />
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                        <p class="text-2xl font-bold text-gray-900">GHC{{ stats.totalRevenue.toLocaleString() }}</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <span
                                :class="stats.growthVsLastMonth.totalRevenue >= 0 ? 'text-green-600' : 'text-red-600'">
                                {{ stats.growthVsLastMonth.totalRevenue >= 0 ? '+' : '' }}{{
                                    stats.growthVsLastMonth.totalRevenue }}%
                            </span>
                            vs last month
                        </p>
                    </div>
                    <div class="p-3 bg-green-50 rounded-full">
                        <DollarSign class="h-6 w-6 text-green-600" />
                    </div>
                </div>
            </div>

            <!-- Average Booking Value -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Avg. Booking Value</p>
                        <p class="text-2xl font-bold text-gray-900">${{ stats.avgBookingValue.toFixed(2) }}</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <span
                                :class="stats.growthVsLastMonth.avgBookingValue >= 0 ? 'text-green-600' : 'text-red-600'">
                                {{ stats.growthVsLastMonth.avgBookingValue >= 0 ? '+' : '' }}{{
                                    stats.growthVsLastMonth.avgBookingValue }}%
                            </span>
                            vs last month
                        </p>
                    </div>
                    <div class="p-3 bg-purple-50 rounded-full">
                        <TrendingUp class="h-6 w-6 text-purple-600" />
                    </div>
                </div>
            </div>

            <!-- Completion Rate -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Completion Rate</p>
                        <p class="text-2xl font-bold text-gray-900">{{ stats.completionRate.toFixed(1) }}%</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <span
                                :class="stats.growthVsLastMonth.completionRate >= 0 ? 'text-green-600' : 'text-red-600'">
                                {{ stats.growthVsLastMonth.completionRate >= 0 ? '+' : '' }}{{
                                    stats.growthVsLastMonth.completionRate }}%
                            </span>
                            vs last month
                        </p>
                    </div>
                    <div class="p-3 bg-orange-50 rounded-full">
                        <CheckCircle class="h-6 w-6 text-orange-600" />
                    </div>
                </div>
            </div>
        </div>
    </template>


</template>

<script setup lang="ts">
import { Calendar, DollarSign, TrendingUp, CheckCircle } from 'lucide-vue-next';
import { useBookingStore } from '~/stores/booking/booking.store';
import type { IBookingStats } from '~/stores/booking/interface/booking.interface';


const bookingStore = useBookingStore();

// Fetch booking stats using useAsyncData
const { data: stats, status, error, refresh } = await useAsyncData('booking-stats', () => {

    return bookingStore.getBookingStats();
}, {
    default: () => ({
        totalBookings: 0,
        totalRevenue: 0,
        avgBookingValue: 0,
        completionRate: 0,
        growthVsLastMonth: {
            totalBookings: 0,
            totalRevenue: 0,
            avgBookingValue: 0,
            completionRate: 0
        }
    } as IBookingStats), lazy: true,
});

// Refresh stats
const refreshStats = () => {
    // Clear data to show loading state
    bookingStore.booking_stats.data = {} as IBookingStats;
    refresh();
};
</script>