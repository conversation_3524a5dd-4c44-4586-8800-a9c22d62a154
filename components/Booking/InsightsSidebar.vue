<template>
    <div :class="[
        'h-full overflow-y-auto',
        isSheet ? 'w-full bg-transparent' : 'w-80 bg-white border-l border-gray-200'
    ]">
        <!-- Header (only show if not in sheet mode) -->
        <div v-if="!isSheet" class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900">Insights</h2>
                <Button variant="ghost" size="sm" @click="$emit('close')">
                    <X class="h-4 w-4" />
                </Button>
            </div>
            <p class="text-sm text-gray-500 mt-1">Real-time booking analytics</p>
        </div>

        <!-- Quick Stats -->
        <div :class="[
            'border-b border-gray-200',
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Today's Overview</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">New Bookings</span>
                    </div>
                    <span class="text-sm font-semibold text-gray-900">{{ todayStats.newBookings }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Revenue</span>
                    </div>
                    <span class="text-sm font-semibold text-gray-900">${{ todayStats.revenue.toLocaleString() }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Pending</span>
                    </div>
                    <span class="text-sm font-semibold text-gray-900">{{ todayStats.pending }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Cancelled</span>
                    </div>
                    <span class="text-sm font-semibold text-gray-900">{{ todayStats.cancelled }}</span>
                </div>
            </div>
        </div>

        <!-- Alerts & Issues -->
        <div :class="[
            'border-b border-gray-200',
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Alerts & Issues</h3>
            <div class="space-y-3">
                <div v-for="alert in alerts" :key="alert.id" 
                     :class="[
                         'p-3 rounded-lg border-l-4',
                         alert.type === 'error' ? 'bg-red-50 border-red-400' :
                         alert.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
                         'bg-blue-50 border-blue-400'
                     ]">
                    <div class="flex items-start space-x-2">
                        <component 
                            :is="alert.icon" 
                            :class="[
                                'h-4 w-4 mt-0.5',
                                alert.type === 'error' ? 'text-red-500' :
                                alert.type === 'warning' ? 'text-yellow-500' :
                                'text-blue-500'
                            ]" 
                        />
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">{{ alert.title }}</p>
                            <p class="text-xs text-gray-600 mt-1">{{ alert.message }}</p>
                            <p class="text-xs text-gray-500 mt-1">{{ alert.time }}</p>
                        </div>
                    </div>
                </div>
                <div v-if="alerts.length === 0" class="text-center py-4">
                    <CheckCircle class="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <p class="text-sm text-gray-500">No issues detected</p>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div :class="[
            'border-b border-gray-200',
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div class="space-y-3">
                <div v-for="activity in recentActivity" :key="activity.id" class="flex items-start space-x-3">
                    <div :class="activity.iconColor" class="p-1 rounded-full">
                        <component :is="activity.icon" class="h-3 w-3" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-xs text-gray-900">{{ activity.message }}</p>
                        <p class="text-xs text-gray-500">{{ activity.time }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div :class="[
            'border-b border-gray-200',
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-2">
                <Button variant="outline" size="sm" class="w-full justify-start" @click="exportBookings">
                    <Download class="h-4 w-4 mr-2" />
                    Export Bookings
                </Button>
                <Button variant="outline" size="sm" class="w-full justify-start" @click="generateReport">
                    <FileText class="h-4 w-4 mr-2" />
                    Generate Report
                </Button>
                <Button variant="outline" size="sm" class="w-full justify-start" @click="bulkActions">
                    <Settings class="h-4 w-4 mr-2" />
                    Bulk Actions
                </Button>
                <Button variant="outline" size="sm" class="w-full justify-start" @click="manageRefunds">
                    <RefreshCw class="h-4 w-4 mr-2" />
                    Manage Refunds
                </Button>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div :class="[
            isSheet ? 'p-4' : 'p-6'
        ]">
            <h3 class="text-sm font-semibold text-gray-900 mb-4">Performance</h3>
            <div class="space-y-4">
                <!-- Conversion Rate -->
                <div>
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-gray-600">Conversion Rate</span>
                        <span class="text-xs font-semibold text-gray-900">{{ performanceMetrics.conversionRate }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            class="bg-green-500 h-2 rounded-full" 
                            :style="{ width: `${performanceMetrics.conversionRate}%` }"
                        ></div>
                    </div>
                </div>

                <!-- Average Response Time -->
                <div>
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-gray-600">Avg Response Time</span>
                        <span class="text-xs font-semibold text-gray-900">{{ performanceMetrics.avgResponseTime }}ms</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            class="bg-blue-500 h-2 rounded-full" 
                            :style="{ width: `${Math.min(performanceMetrics.avgResponseTime / 10, 100)}%` }"
                        ></div>
                    </div>
                </div>

                <!-- Customer Satisfaction -->
                <div>
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-gray-600">Customer Satisfaction</span>
                        <span class="text-xs font-semibold text-gray-900">{{ performanceMetrics.satisfaction }}/5</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            class="bg-purple-500 h-2 rounded-full" 
                            :style="{ width: `${(performanceMetrics.satisfaction / 5) * 100}%` }"
                        ></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
    X, 
    AlertTriangle, 
    AlertCircle, 
    Info, 
    CheckCircle, 
    Plus, 
    Minus, 
    Download,
    FileText,
    Settings,
    RefreshCw
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import type { IBooking } from '~/stores/booking/model/booking.model';

const props = defineProps<{
    bookings: IBooking[];
    isSheet?: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
}>();

// Today's stats (computed from bookings)
const todayStats = computed(() => {
    const today = new Date().toDateString();
    const todayBookings = props.bookings.filter(booking => 
        new Date(booking.booking_schedule_date).toDateString() === today
    );

    return {
        newBookings: todayBookings.length,
        revenue: todayBookings.reduce((sum, booking) => sum + booking.total_amount, 0),
        pending: todayBookings.filter(b => b.status.toLowerCase() === 'pending').length,
        cancelled: todayBookings.filter(b => b.status.toLowerCase() === 'cancelled').length
    };
});

// Alerts and issues
const alerts = ref([
    {
        id: 1,
        type: 'warning',
        title: 'High Cancellation Rate',
        message: '15% cancellation rate detected for Route A',
        time: '10 minutes ago',
        icon: AlertTriangle
    },
    {
        id: 2,
        type: 'info',
        title: 'Peak Hours Approaching',
        message: 'Expect increased booking volume in 2 hours',
        time: '30 minutes ago',
        icon: Info
    }
]);

// Recent activity
const recentActivity = ref([
    { 
        id: 1, 
        message: 'Booking #BK001234 confirmed', 
        time: '2 min ago', 
        icon: Plus, 
        iconColor: 'bg-green-100 text-green-600' 
    },
    { 
        id: 2, 
        message: 'Refund processed for #BK001230', 
        time: '5 min ago', 
        icon: RefreshCw, 
        iconColor: 'bg-blue-100 text-blue-600' 
    },
    { 
        id: 3, 
        message: 'Booking #BK001228 cancelled', 
        time: '12 min ago', 
        icon: Minus, 
        iconColor: 'bg-red-100 text-red-600' 
    }
]);

// Performance metrics (mock data)
const performanceMetrics = ref({
    conversionRate: 78,
    avgResponseTime: 245,
    satisfaction: 4.2
});

// Quick action handlers
const exportBookings = () => {
    // Handle export functionality
    console.log('Exporting bookings...');
};

const generateReport = () => {
    // Handle report generation
    console.log('Generating report...');
};

const bulkActions = () => {
    // Handle bulk actions
    console.log('Opening bulk actions...');
};

const manageRefunds = () => {
    // Handle refund management
    console.log('Opening refund management...');
};
</script>
