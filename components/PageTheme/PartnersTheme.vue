<template>
  <div class="partners-theme">
    <!-- Partners-specific gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-teal-50 via-cyan-50 to-blue-50 -z-10" />
    
    <!-- Business network pattern -->
    <div class="absolute inset-0 opacity-6 -z-5">
      <div class="flex items-center justify-center h-full">
        <div class="relative w-96 h-96">
          <!-- Central hub -->
          <div class="absolute top-1/2 left-1/2 w-8 h-8 bg-teal-300 rounded-full transform -translate-x-1/2 -translate-y-1/2" />
          
          <!-- Surrounding nodes -->
          <div v-for="(angle, i) in [0, 60, 120, 180, 240, 300]" :key="i" 
               class="absolute w-6 h-6 bg-cyan-200 rounded-full"
               :style="{
                 top: '50%',
                 left: '50%',
                 transform: `translate(-50%, -50%) rotate(${angle}deg) translateY(-120px) rotate(-${angle}deg)`
               }">
            <!-- Connection lines -->
            <div class="absolute top-1/2 left-1/2 w-px h-24 bg-blue-200 origin-bottom transform -translate-x-1/2 -translate-y-full"
                 :style="{ transform: `translate(-50%, -100%) rotate(${angle + 180}deg)` }" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- Floating partner icons -->
    <div class="absolute top-24 right-32 text-teal-200 opacity-30">
      <Building2 class="w-18 h-18 animate-pulse" />
    </div>
    <div class="absolute bottom-32 left-32 text-cyan-200 opacity-25">
      <Handshake class="w-16 h-16 animate-bounce" />
    </div>
    <div class="absolute top-2/3 right-1/5 text-blue-200 opacity-20">
      <TrendingUp class="w-14 h-14 animate-pulse" style="animation-duration: 3s;" />
    </div>
    
    <slot />
  </div>
</template>

<script setup lang="ts">
import { Building2, Handshake, TrendingUp } from 'lucide-vue-next'
</script>

<style scoped>
.partners-theme {
  position: relative;
  min-height: 100vh;
}
</style>
