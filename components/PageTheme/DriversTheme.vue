<template>
  <div class="drivers-theme">
    <!-- Drivers-specific gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50 -z-10" />
    
    <!-- Road/path pattern -->
    <div class="absolute inset-0 opacity-8 -z-5">
      <div class="flex flex-col justify-center h-full space-y-16">
        <div v-for="i in 4" :key="i" class="relative">
          <!-- Road line -->
          <div class="w-full h-1 bg-gray-200" />
          <!-- Dashed center line -->
          <div class="absolute top-0 left-0 w-full h-px bg-gray-300 border-t-2 border-dashed border-gray-300" />
        </div>
      </div>
    </div>
    
    <!-- Floating driver icons -->
    <div class="absolute top-28 right-24 text-slate-300 opacity-30">
      <UserCheck class="w-16 h-16 animate-pulse" />
    </div>
    <div class="absolute bottom-40 left-24 text-gray-300 opacity-25">
      <Car class="w-14 h-14 animate-bounce" />
    </div>
    <div class="absolute top-1/2 right-1/3 text-zinc-300 opacity-20">
      <Clock class="w-12 h-12 animate-spin" style="animation-duration: 15s;" />
    </div>
    
    <slot />
  </div>
</template>

<script setup lang="ts">
import { UserCheck, Car, Clock } from 'lucide-vue-next'
</script>

<style scoped>
.drivers-theme {
  position: relative;
  min-height: 100vh;
}
</style>
