<template>
  <div class="routes-theme">
    <!-- Routes-specific gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-purple-50 via-violet-50 to-indigo-50 -z-10" />
    
    <!-- Map-inspired decorative lines -->
    <div class="absolute inset-0 opacity-10 -z-5">
      <svg class="w-full h-full" viewBox="0 0 1000 1000" fill="none">
        <path d="M100 200 Q300 100 500 200 T900 200" stroke="currentColor" stroke-width="2" class="text-purple-300" />
        <path d="M100 400 Q400 300 700 400 T900 400" stroke="currentColor" stroke-width="2" class="text-violet-300" />
        <path d="M100 600 Q250 500 500 600 T900 600" stroke="currentColor" stroke-width="2" class="text-indigo-300" />
        <path d="M100 800 Q350 700 600 800 T900 800" stroke="currentColor" stroke-width="2" class="text-purple-300" />
      </svg>
    </div>
    
    <!-- Floating route icons -->
    <div class="absolute top-20 right-24 text-purple-200 opacity-25">
      <MapPin class="w-18 h-18 animate-pulse" />
    </div>
    <div class="absolute bottom-40 left-24 text-violet-200 opacity-20">
      <Navigation class="w-14 h-14 animate-spin" style="animation-duration: 8s;" />
    </div>
    
    <slot />
  </div>
</template>

<script setup lang="ts">
import { MapPin, Navigation } from 'lucide-vue-next'
</script>

<style scoped>
.routes-theme {
  position: relative;
  min-height: 100vh;
}
</style>
