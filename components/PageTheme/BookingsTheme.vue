<template>
  <div class="bookings-theme">
    <!-- Bookings-specific gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 -z-10" />
    
    <!-- Calendar-inspired decorative pattern -->
    <div class="absolute top-0 left-0 w-full h-full opacity-5 -z-5">
      <div class="grid grid-cols-12 gap-4 h-full p-8">
        <div v-for="i in 48" :key="i" class="bg-green-200 rounded-sm" />
      </div>
    </div>
    
    <!-- Floating booking icons -->
    <div class="absolute top-16 right-16 text-green-200 opacity-20">
      <Calendar class="w-20 h-20 animate-pulse" />
    </div>
    <div class="absolute bottom-32 left-16 text-emerald-200 opacity-15">
      <CheckCircle class="w-16 h-16 animate-bounce" />
    </div>
    
    <slot />
  </div>
</template>

<script setup lang="ts">
import { Calendar, CheckCircle } from 'lucide-vue-next'
</script>

<style scoped>
.bookings-theme {
  position: relative;
  min-height: 100vh;
}
</style>
