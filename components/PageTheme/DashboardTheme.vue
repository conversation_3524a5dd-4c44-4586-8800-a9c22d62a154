<template>
  <div class="dashboard-theme">
    <!-- Dashboard-specific gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 -z-10" />
    
    <!-- Floating decorative elements -->
    <div class="absolute top-20 right-20 w-32 h-32 bg-blue-100 rounded-full opacity-20 blur-xl animate-pulse" />
    <div class="absolute bottom-20 left-20 w-24 h-24 bg-purple-100 rounded-full opacity-30 blur-lg animate-bounce" />
    
    <slot />
  </div>
</template>

<style scoped>
.dashboard-theme {
  position: relative;
  min-height: 100vh;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}
</style>
