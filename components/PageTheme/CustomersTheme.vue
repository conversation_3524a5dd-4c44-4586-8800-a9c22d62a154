<template>
  <div class="customers-theme">
    <!-- Customers-specific gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-rose-50 via-pink-50 to-red-50 -z-10" />
    
    <!-- User connection pattern -->
    <div class="absolute inset-0 opacity-8 -z-5">
      <div class="grid grid-cols-8 gap-8 h-full p-12">
        <div v-for="i in 32" :key="i" class="relative">
          <div class="w-4 h-4 bg-rose-200 rounded-full mx-auto" />
          <div v-if="i % 3 === 0" class="absolute top-2 left-1/2 w-px h-8 bg-pink-200 transform -translate-x-1/2" />
          <div v-if="i % 4 === 0" class="absolute top-2 left-2 w-8 h-px bg-red-200" />
        </div>
      </div>
    </div>
    
    <!-- Floating customer icons -->
    <div class="absolute top-20 right-28 text-rose-200 opacity-25">
      <Users class="w-20 h-20 animate-pulse" />
    </div>
    <div class="absolute bottom-36 left-28 text-pink-200 opacity-20">
      <Heart class="w-14 h-14 animate-bounce" />
    </div>
    <div class="absolute top-1/3 right-1/4 text-red-200 opacity-15">
      <Star class="w-12 h-12 animate-spin" style="animation-duration: 12s;" />
    </div>
    
    <slot />
  </div>
</template>

<script setup lang="ts">
import { Users, Heart, Star } from 'lucide-vue-next'
</script>

<style scoped>
.customers-theme {
  position: relative;
  min-height: 100vh;
}
</style>
