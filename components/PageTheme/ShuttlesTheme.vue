<template>
  <div class="shuttles-theme">
    <!-- Shuttles-specific gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 -z-10" />
    
    <!-- Vehicle track pattern -->
    <div class="absolute inset-0 opacity-5 -z-5">
      <div class="flex flex-col space-y-8 h-full justify-center">
        <div v-for="i in 6" :key="i" class="flex space-x-4">
          <div v-for="j in 20" :key="j" class="w-8 h-2 bg-orange-300 rounded-full" />
        </div>
      </div>
    </div>
    
    <!-- Floating shuttle icons -->
    <div class="absolute top-24 right-20 text-orange-200 opacity-30">
      <Truck class="w-16 h-16 animate-bounce" />
    </div>
    <div class="absolute bottom-28 left-20 text-amber-200 opacity-25">
      <Zap class="w-12 h-12 animate-pulse" />
    </div>
    <div class="absolute top-1/2 right-1/3 text-yellow-200 opacity-15">
      <Settings class="w-10 h-10 animate-spin" style="animation-duration: 10s;" />
    </div>
    
    <slot />
  </div>
</template>

<script setup lang="ts">
import { Truck, Zap, Settings } from 'lucide-vue-next'
</script>

<style scoped>
.shuttles-theme {
  position: relative;
  min-height: 100vh;
}
</style>
