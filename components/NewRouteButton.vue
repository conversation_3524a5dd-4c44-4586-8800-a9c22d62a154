<template>
    <Dialog :open="isDialogueOpen" @update:open="handleOnDialogOpen">
        <DialogTrigger>
            <Button
                class="items-center hidden px-3 py-2 text-sm font-medium leading-4 text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm sm:inline-flex hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                <PlusCircle class="w-4 h-4 mr-1 -ml-1" />
                New Route
            </Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-[550px]">
            <DialogHeader class="text-2xl font-bold">Create New Route</DialogHeader>
            <DialogDescription>Plan your journey with ease using our interactive map.</DialogDescription>
            <form class="flex flex-col gap-5">
                <div class="relative">
                    <div class="flex flex-col gap-6">
                        <div class="flex items-center gap-4">
                            <div class="bg-blue-500 rounded-full p-2">
                                <MapPin class="w-4 h-4 text-white" />
                            </div>
                            <FormField v-slot="{ componentField }" name="origin" class="flex-1">
                                <FormItem class="w-full">
                                    <FormLabel class="sr-only">Origin</FormLabel>
                                    <FormControl>
                                        <Popover v-model:open="originOpen">
                                            <PopoverTrigger as-child>
                                                <Button variant="outline" role="combobox" :aria-expanded="originOpen"
                                                    class="w-full justify-between">
                                                    {{ originData.name || "Enter origin" }}
                                                    <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent class="w-full p-0">
                                                <Command>
                                                    <CommandInput ref="originInput" v-bind="componentField"
                                                        placeholder="Enter origin"
                                                        @input="debounce(() => fetchPlaces('origin'), 300)" />
                                                    <CommandEmpty>No locations found.</CommandEmpty>
                                                    <CommandList>
                                                        <CommandGroup>
                                                            <CommandItem v-for="prediction in originPredictions"
                                                                :key="prediction.placeId" :value="prediction"
                                                                @select="handleSelectedLocationPrediction($event, true)">
                                                                {{ prediction.description }}
                                                            </CommandItem>
                                                        </CommandGroup>
                                                    </CommandList>
                                                </Command>
                                            </PopoverContent>
                                        </Popover>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>
                        </div>

                        <div class="flex items-center gap-4" >
                            <div class="bg-red-500 rounded-full p-2">
                                <Flag class="w-4 h-4 text-white" />
                            </div>
                            <FormField v-slot="{ componentField }" name="destination" class="flex-1">
                                <FormItem class="w-full">
                                    <FormLabel class="sr-only">Destination</FormLabel>
                                    <FormControl>
                                        <Popover v-model:open="destinationOpen" >
                                            <PopoverTrigger as-child>
                                                <Button variant="outline" role="combobox"
                                                    :aria-expanded="destinationOpen" class="w-full justify-between">
                                                    {{ destinationData.name || "Enter destination" }}
                                                    <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent class="w-full p-0">
                                                <Command>
                                                    <CommandInput ref="destinationInput" v-bind="componentField"
                                                        placeholder="Enter destination"
                                                        @input="debounce(() => fetchPlaces('destination'), 300)" />
                                                    <CommandEmpty>No locations found.</CommandEmpty>
                                                    <CommandList>
                                                        <CommandGroup>
                                                            <CommandItem v-for="prediction in destinationPredictions"
                                                                :key="prediction.placeId"
                                                                :value="prediction"
                                                                @select="handleSelectedLocationPrediction($event, false)">
                                                                {{ prediction.description }}
                                                            </CommandItem>
                                                        </CommandGroup>
                                                    </CommandList>
                                                </Command>
                                            </PopoverContent>
                                        </Popover>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>
                        </div>
                    </div>
                </div>
                <Separator class="my-4" />
                <div class="flex gap-4">
                    <FormField v-slot="{ componentField }" name="shuttle">
                        <FormItem class="flex-1">
                            <FormLabel>Assigned Shuttle</FormLabel>
                            <Select id="assignedShuttle" @update:open="handleAvailableShuttleSelect"
                                v-bind="componentField">
                                <FormControl>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a shuttle" />
                                    </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                    <div v-if="shuttleStore.isLoading">
                                        <Loader2 class="animate-spin"></Loader2>
                                    </div>
                                    <template v-else>
                                        <SelectGroup>
                                            <SelectLabel>Available Shuttles</SelectLabel>
                                            <SelectItem v-for="shuttle in shuttleStore.shuttles"
                                                :value="shuttle.id">
                                                {{ shuttle.model }} | {{ shuttle.plate_number }}
                                            </SelectItem>
                                        </SelectGroup>
                                    </template>
                                </SelectContent>
                            </Select>
                        </FormItem>
                    </FormField>
                    <FormField v-slot="{ componentField }" name="baseFare">
                        <FormItem class="flex-1">
                            <FormLabel>Base Fare (GHC)</FormLabel>
                            <FormControl>
                                <Input v-bind="componentField" id="baseFare" type="number" min="0" />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    </FormField>
                </div>
                <div class="flex gap-4">
                    <FormField v-slot="{ componentField }" name="scheduleDate">
                        <FormItem class="flex-1">
                            <FormLabel>Start Date</FormLabel>
                            <FormControl>
                                <Input v-bind="componentField" id="scheduleDate" type="date" />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    </FormField>
                    <FormField v-slot="{ componentField }" name="scheduleTime">
                        <FormItem class="flex-1">
                            <FormLabel>Start Time</FormLabel>
                            <FormControl>
                                <Input v-bind="componentField" id="scheduleTime" type="time" />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    </FormField>
                </div>
                <FormField v-slot="{ componentField }" name="repeatSchedule">
                    <FormItem>
                        <FormLabel>Repeat Schedule</FormLabel>
                        <Select v-bind="componentField">
                            <FormControl>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select repeat option" />
                                </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                                <SelectItem value="none">Do not repeat</SelectItem>
                                <SelectItem value="daily">Daily</SelectItem>
                                <SelectItem value="weekly">Weekly</SelectItem>
                                <SelectItem value="monthly">Monthly</SelectItem>
                            </SelectContent>
                        </Select>
                        <FormMessage />
                    </FormItem>
                </FormField>

                <Button @click="createNewRoute" :disabled="routeStore.isLoadingNewRoute">Add New Route
                    <span v-if="routeStore.isLoadingNewRoute">
                        <Loader2 class="animate-spin ml-2"></Loader2>
                    </span>
                </Button>
            </form>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod';
import { useForm } from 'vee-validate';
import { toast } from 'vue-sonner';
import { z } from 'zod';
import type { NewRouteDTO } from '~/stores/route/dto/newRoute.dto';
import type { IRoute } from '~/stores/route/model/route.model';
import { useRouteStore } from '~/stores/route/route.store';
import type { IShuttle } from '~/stores/shuttle/model/shuttle.model';
import { useShuttleStore } from '~/stores/shuttle/shuttle.store';
import { MapPin, Flag, ChevronsUpDown, PlusCircle } from 'lucide-vue-next';


const routeStore = useRouteStore()
const shuttleStore = useShuttleStore();

// DIALOG CONTROL
const isDialogueOpen = ref(false)
const handleOnDialogOpen = (isOpen: boolean) => {
    isDialogueOpen.value = isOpen
}
// end of DIALOG CONTROL

// CREATE NEW ROUTE
const handleAvailableShuttleSelect = (isOpen: boolean) => {
    // Once open check if there are available shuttles if not get them
    if (isOpen && !shuttleStore.hasShuttles) {
        // Get the shuttles
        useAsyncData<IShuttle[]>('shuttle', () => shuttleStore.getShuttles())
    }
}

const formSchema = toTypedSchema(z.object({
    origin: z.string(),
    destination: z.string(),
    shuttle: z.string(),
    baseFare: z.number().min(0),
    scheduleDate: z.string(),
    scheduleTime: z.string(),
    repeatSchedule: z.enum(['none', 'daily', 'weekly', 'monthly'])
}))

const { handleSubmit, values } = useForm({
    validationSchema: formSchema,
})

const originData = ref({ name: '', lat: 0, long: 0 })
const destinationData = ref({ name: '', lat: 0, long: 0 })

const originPredictions = ref<{ description: string, placeId: string }[]>([]);
const destinationPredictions = ref<{ description: string, placeId: string }[]>([]);

const originOpen = ref(false);
const destinationOpen = ref(false);


const fetchPlaces = async (fieldName: 'origin' | 'destination') => {

    const input = fieldName === 'origin' ? values.origin : values.destination;
    if (!input) return;

    try {
        const predictions = await routeStore.getLocationPrediction(input);
        if (fieldName === 'origin') {
            originPredictions.value = predictions;
        } else {
            destinationPredictions.value = predictions;
        }
    } catch (error) {
        console.error('Error fetching places:', error);
    }
};

let debounceTimeout: ReturnType<typeof setTimeout>;

const debounce = (func: Function, wait: number) => {
    clearTimeout(debounceTimeout);
    debounceTimeout = setTimeout(() => {
        func();
    }, wait);
};

const handleSelectedLocationPrediction = async (event: any, isOrigin?: boolean) => {
    if (typeof event.detail.value.placeId === 'string') {
        console.log("GETTING EVENT")
        //Get the coordinates from the prediction
        const coords = await routeStore.getPlaceCoordinates(event.detail.value.placeId)

        const locationData:{name :string, lat:number, long:number} = {
            name : event.detail.value.description,
            lat : coords.lat,
            long : coords.long
        }
        
        // Set the data
        isOrigin ? originData.value = locationData : destinationData.value = locationData;
    }

    // Close the popover
    isOrigin ? originOpen.value = false : destinationOpen.value = false;


}

const createNewRoute = handleSubmit(async (values) => {
    // Prep data
    const newRouteData = ref<NewRouteDTO>({
        "baseFare": values.baseFare,
        "destinationLocation": {
            "name": destinationData.value.name.split(',')[0],
            "coord": {
                "lat": destinationData.value.lat,
                "long": destinationData.value.long
            }
        },
        "originLocation": {
            "name": originData.value.name.split(',')[0],
            "coord": {
                "lat": originData.value.lat,
                "long": originData.value.long
            }
        },
        "shuttleId": values.shuttle,
        "scheduleDate": `${values.scheduleDate}T${values.scheduleTime}`,
        // "repeatSchedule": values.repeatSchedule as 'none' | 'daily' | 'weekly' | 'monthly'
    });


    await useAsyncData<IRoute>('newRoute', () => routeStore.createNewRoute(newRouteData.value))

    // Handle error
    if (routeStore.failedNewRoute) {
        toast.error('Failed to create new route')
        return;
    }

    // Show success sonner
    toast.success('New route added', {
        description: `${newRouteData.value.originLocation.name} - to - ${newRouteData.value.destinationLocation.name}`,
    })

    // Success
    isDialogueOpen.value = false //Close the modal
})
</script>