{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "^1.1.5", "@nuxtjs/tailwindcss": "^6.12.0", "@types/canvas-confetti": "^1.6.4", "@types/node": "^20.11.30", "@types/qrcode": "^1.5.5", "autoprefixer": "^10.4.19", "nuxt": "^3.11.1", "nuxt-icon": "^0.6.10", "postcss": "^8.4.38", "shadcn-nuxt": "^0.10.2", "tailwindcss": "^3.4.3", "typescript": "^5.4.3"}, "dependencies": {"@pinia/nuxt": "^0.5.1", "@tanstack/vue-table": "^8.15.3", "@vee-validate/zod": "^4.12.6", "@vueuse/core": "^10.11.1", "axios": "^1.6.8", "canvas-confetti": "^1.9.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jsbarcode": "^3.11.6", "lucide-vue-next": "^0.363.0", "motion": "^10.17.0", "pinia": "^2.1.7", "qrcode": "^1.5.3", "radix-vue": "^1.8.3", "reka-ui": "^2.4.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ts-luxon": "^4.5.2", "vee-validate": "^4.12.6", "vue-google-autocomplete": "^1.1.4", "vue-sonner": "^1.1.2", "vue3-apexcharts": "^1.5.3", "vue3-google-map": "^0.20.0", "zod": "^3.23.6"}, "optionalDependencies": {"@rollup/rollup-linux-x64-musl": "4.9.5"}, "overrides": {"vue": "latest"}}